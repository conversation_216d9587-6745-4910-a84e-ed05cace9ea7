#
# Copyright (c) 2003-2025 <PERSON> (chris at kohlhoff dot com)
#
# Distributed under the Boost Software License, Version 1.0. (See accompanying
# file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
#

lib socket ; # <PERSON><PERSON><PERSON>IS
lib nsl ; # <PERSON><PERSON><PERSON>IS
lib ws2_32 ; # NT
lib mswsock ; # NT
lib ipv6 ; # HPUX
lib network ; # HAIKU

project
  : requirements
    <library>/boost/system//boost_system
    <library>/boost/chrono//boost_chrono
    <library>/boost/thread//boost_thread
    <define>BOOST_ALL_NO_LIB=1
    <threading>multi
    <target-os>solaris:<library>socket
    <target-os>solaris:<library>nsl
    <target-os>windows:<define>_WIN32_WINNT=0x0501
    <target-os>windows,<toolset>gcc:<library>ws2_32
    <target-os>windows,<toolset>gcc:<library>mswsock
    <target-os>windows,<toolset>gcc-cygwin:<define>__USE_W32_SOCKETS
    <target-os>hpux,<toolset>gcc:<define>_XOPEN_SOURCE_EXTENDED
    <target-os>hpux:<library>ipv6
    <target-os>haiku:<library>network
  ;

obj timer1.obj : timer1/timer.cpp ;
exe timer1 : timer1.obj ;

obj timer2.obj : timer2/timer.cpp ;
exe timer2 : timer2.obj ;

obj timer3.obj : timer3/timer.cpp ;
exe timer3 : timer3.obj ;

obj timer4.obj : timer4/timer.cpp ;
exe timer4 : timer4.obj ;

obj timer5.obj : timer5/timer.cpp ;
exe timer5 : timer5.obj ;

obj daytime1_client.obj : daytime1/client.cpp ;
exe daytime1_client : daytime1_client.obj ;

obj daytime2_server.obj : daytime2/server.cpp ;
exe daytime2_server : daytime2_server.obj ;

obj daytime3_server.obj : daytime3/server.cpp ;
exe daytime3_server : daytime3_server.obj ;

obj daytime4_client.obj : daytime4/client.cpp ;
exe daytime4_client : daytime4_client.obj ;

obj daytime5_server.obj : daytime5/server.cpp ;
exe daytime5_server : daytime5_server.obj ;

obj daytime6_server.obj : daytime6/server.cpp ;
exe daytime6_server : daytime6_server.obj ;

obj daytime7_server.obj : daytime7/server.cpp ;
exe daytime7_server : daytime7_server.obj ;
