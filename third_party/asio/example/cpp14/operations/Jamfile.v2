#
# Copyright (c) 2003-2025 <PERSON> (chris at kohlhoff dot com)
#
# Distributed under the Boost Software License, Version 1.0. (See accompanying
# file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
#

lib socket ; # <PERSON><PERSON><PERSON>IS
lib nsl ; # <PERSON><PERSON><PERSON>IS
lib ws2_32 ; # NT
lib mswsock ; # NT
lib ipv6 ; # HPUX
lib network ; # HAIKU

project
  : requirements
    <library>/boost/system//boost_system
    <library>/boost/chrono//boost_chrono
    <define>BOOST_ALL_NO_LIB=1
    <threading>multi
    <target-os>solaris:<library>socket
    <target-os>solaris:<library>nsl
    <target-os>windows:<define>_WIN32_WINNT=0x0501
    <target-os>windows,<toolset>gcc:<library>ws2_32
    <target-os>windows,<toolset>gcc:<library>mswsock
    <target-os>windows,<toolset>gcc-cygwin:<define>__USE_W32_SOCKETS
    <target-os>hpux,<toolset>gcc:<define>_XOPEN_SOURCE_EXTENDED
    <target-os>hpux:<library>ipv6
    <target-os>haiku:<library>network
  ;

exe callback_wrapper : callback_wrapper.cpp ;
exe c_callback_wrapper : c_callback_wrapper.cpp ;
exe composed_1 : composed_1.cpp ;
exe composed_2 : composed_2.cpp ;
exe composed_3 : composed_3.cpp ;
exe composed_4 : composed_4.cpp ;
exe composed_5 : composed_5.cpp ;
exe composed_6 : composed_6.cpp ;
exe composed_7 : composed_7.cpp ;
exe composed_8 : composed_8.cpp ;
