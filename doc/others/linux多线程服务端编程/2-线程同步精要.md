# 纲要
1. 尽量减少共享对象：优先避免共享对象，减少需要同步的场景。如果必须共享，优先考虑不可变（immutable）对象。对于可修改的对象，应采用同步措施充分保护。

2. 使用高级并发构件：优先选择使用高级并发编程构件，如任务队列（TaskQueue）、生产者-消费者队列（Producer-Consumer Queue）、倒计时闩锁（CountDownLatch）等，这些工具能够简化同步操作并提高代码的可读性和可靠性。

3. 慎用底层同步原语：在不得已使用底层同步原语（如非递归互斥器和条件变量）时，要避免使用复杂或易出错的机制，如读写锁和信号量。

4. 避免自己编写锁自由代码：除了使用原子操作（atomic）外，不要自行编写无锁（lock-free）代码，也不要随意使用内核级同步原语。避免根据臆测来选择同步方式，如盲目比较自旋锁与互斥锁的性能。

# mutex
1. 使用RAII
2. 只用非递归的mutex
3. 不手动调用lock()和unlock()
4. 每次构造guard的时候要思考已使用的锁，防止顺序不同，导致死锁
# 条件变量
wait端：
1. 必须与mutex一起使用，该布尔表达式的读写需受此mutex保护。
2. 在mutex上锁的时候才能调用wait()
3. 把判断布尔条件和wait()放到while循环中
signal/broadcast端：
1. 不一定要在mutex已上锁的情况下调用signal
2， 在siganl之前一定要修改布尔表达式
3. 修改布尔表达式通常要用mutex保护
4. 注意区分signal与broadcast，前者表示资源可用，后者表示状态变化

# 倒计时(countdownlatch)
1. 主线程发起多个子线程，等这些子线程各自都完成一定的任务之后，主线程才继续执行，用于主线程等待多个子线程完成初始化
2. 主线程发起多个子线程，主线程完成其他一些任务之后通知所有子线程开始执行，通常用于多个子线程等待主线程发出“去跑”命令

# 不要用读写锁和信号量
1. 读写锁使用时容易在readlock保护的函数中调用了会修改状态的函数
2. 如果临界区很小，锁竞争不激烈，那么mutex往往会更快
3. readlock可能允许提升也可能不允许，不管如何都可能造成迭代器失效（修改后再读），或者造成死锁
4. 通常 reader lock 是可重入的，writer lock是不可重入的。但是为了防止 writer饥饿，writer lock通常会阻塞后来的readerlock，因此reader lock在重人的时候可能死锁。另外，在追求低延迟读取的场合也不适用读写锁。
5. 信号量完全可以使用条件变量配合互斥器可以完全替代其功能。而且更不易用错

# 线程安全的singleton实现
```cpp
// Use of this source code is governed by a BSD-style license
// that can be found in the License file.
//
// Author: Shuo Chen (chenshuo at chenshuo dot com)

#ifndef  BASE_SINGLETON_H
#define  BASE_SINGLETON_H

#include "zexuan/base/noncopyable.hpp"

#include <assert.h>
#include <pthread.h>
#include <stdlib.h> // atexit

namespace zexuan
{

namespace detail
{
// This doesn't detect inherited member functions!
// http://stackoverflow.com/questions/1966362/sfinae-to-check-for-inherited-member-functions
template<typename T>
struct has_no_destroy
{
  template <typename C> static char test(decltype(&C::no_destroy));
  template <typename C> static int32_t test(...);
  const static bool value = sizeof(test<T>(0)) == 1;
};
}  // namespace detail

template<typename T>
class Singleton : noncopyable
{
 public:
  Singleton() = delete;
  ~Singleton() = delete;

  static T& instance()
  {
    pthread_once(&ponce_, &Singleton::init);
    assert(value_ != NULL);
    return *value_;
  }

 private:
  static void init()
  {
    value_ = new T();
    if (!detail::has_no_destroy<T>::value)
    {
      ::atexit(destroy);
    }
  }

  static void destroy()
  {
    typedef char T_must_be_complete_type[sizeof(T) == 0 ? -1 : 1];
    T_must_be_complete_type dummy; (void) dummy;

    delete value_;
    value_ = NULL;
  }

 private:
  static pthread_once_t ponce_;
  static T*             value_;
};

template<typename T>
pthread_once_t Singleton<T>::ponce_ = PTHREAD_ONCE_INIT;

template<typename T>
T* Singleton<T>::value_ = NULL;

}  // namespace zexuan

#endif  //  BASE_SINGLETON_H
```

# 归纳与总结
1. 线程同步的四项原则，尽量用高层同步设施(线程池、队列、倒计时);
2. 使用普通互斥器和条件变量完成剩余的同步任务，采用RAI惯用手法(idiom)和 Scoped Locking.