# classify Plugin CMakeLists.txt

# 创建 classify_plugin 动态库
add_library(classify_plugin SHARED
    src/classify_plugin.cpp
)

# 链接必要的库
target_link_libraries(classify_plugin
    PRIVATE
        plugin_interface
)

# 设置包含目录
target_include_directories(classify_plugin PRIVATE
    ${CMAKE_SOURCE_DIR}/core/include
    ${CMAKE_SOURCE_DIR}/interface/include
)

# 设置输出目录
set_target_properties(classify_plugin PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/libs/plugins"
)
