# 数据处理插件
# Data Processor Plugin - Using unified PluginBase
add_library(message_plugin SHARED
    src/message_plugin.cpp
)

# 设置包含目录
target_include_directories(message_plugin
    PRIVATE
        ${CMAKE_SOURCE_DIR}/interface/include
        ${CMAKE_SOURCE_DIR}/core/include
)

# 链接依赖库
target_link_libraries(message_plugin
    PRIVATE
        plugin_interface
)

# 设置输出目录
set_target_properties(message_plugin PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/libs/plugins
)
