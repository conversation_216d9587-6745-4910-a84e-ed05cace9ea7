#ifndef  BASE_TYPES_H
#define  BASE_TYPES_H

#include <stdint.h>
#include <string.h>  // memset
#include <string>
#include <type_traits>

#ifndef NDEBUG
#include <assert.h>
#endif

///
/// The most common stuffs.
///
namespace zexuan
{

using std::string;

inline void memZero(void* p, size_t n)
{
  memset(p, 0, n);
}

template<typename To, typename From>     // use like this: down_cast<T*>(foo);
inline To down_cast(From* f)                     // so we only accept pointers
{
  // 编译时类型检查：确保 To 是 From* 的子类型
  static_assert(std::is_convertible_v<From*, To>, "Invalid down cast");

#if !defined(NDEBUG) && !defined(GOOGLE_PROTOBUF_NO_RTTI)
  assert(f == NULL || dynamic_cast<To>(f) != NULL);  // RTTI: debug mode only!
#endif
  return static_cast<To>(f);
}

}  // namespace zexuan

#endif  //  BASE_TYPES_H
