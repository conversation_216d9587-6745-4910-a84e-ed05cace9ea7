  //
// This is a public header file, it must only include public header files.

#ifndef  NET_EVENTLOOPTHREAD_H
#define  NET_EVENTLOOPTHREAD_H

#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <functional>

#include "zexuan/base/noncopyable.hpp"

namespace zexuan
{
namespace net
{

class EventLoop;

class EventLoopThread : zexuan::base::noncopyable
{
 public:
  typedef std::function<void(EventLoop*)> ThreadInitCallback;

  EventLoopThread(const ThreadInitCallback& cb = ThreadInitCallback(),
                  const string& name = string());
  ~EventLoopThread();
  EventLoop* startLoop();

 private:
  void threadFunc();

  EventLoop* loop_;
  std::atomic<bool> exiting_;
  std::thread thread_;
  std::mutex mutex_;
  std::condition_variable cond_;
  ThreadInitCallback callback_;
  string name_;  // 保留名称用于调试
};

}  // namespace net
}  // namespace zexuan

#endif  //  NET_EVENTLOOPTHREAD_H

