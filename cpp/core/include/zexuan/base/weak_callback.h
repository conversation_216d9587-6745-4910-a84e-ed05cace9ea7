#ifndef ZEXUAN_BASE_WEAK_CALLBACK_H
#define ZEXUAN_BASE_WEAK_CALLBACK_H

#include <functional>
#include <memory>

namespace zexuan {

// 现代 C++ 风格的 weak callback 实现
template<typename T, typename... Args>
auto make_weak_callback(std::weak_ptr<T> weak_ptr, void (T::*method)(Args...)) {
    return [weak_ptr, method](Args... args) {
        if (auto strong_ptr = weak_ptr.lock()) {
            (strong_ptr.get()->*method)(args...);
        }
    };
}

// const 成员函数版本
template<typename T, typename... Args>
auto make_weak_callback(std::weak_ptr<T> weak_ptr, void (T::*method)(Args...) const) {
    return [weak_ptr, method](Args... args) {
        if (auto strong_ptr = weak_ptr.lock()) {
            (strong_ptr.get()->*method)(args...);
        }
    };
}

// 通用函数对象版本
template<typename T, typename F>
auto make_weak_callback(std::weak_ptr<T> weak_ptr, F&& func) {
    return [weak_ptr, func = std::forward<F>(func)](auto&&... args) {
        if (auto strong_ptr = weak_ptr.lock()) {
            func(strong_ptr.get(), std::forward<decltype(args)>(args)...);
        }
    };
}

}  // namespace zexuan

#endif  // ZEXUAN_BASE_WEAK_CALLBACK_H
