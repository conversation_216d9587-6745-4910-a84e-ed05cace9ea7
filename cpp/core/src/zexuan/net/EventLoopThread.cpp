  
#include "zexuan/net/EventLoopThread.h"

#include "zexuan/net/EventLoop.h"
#include <cassert>

using namespace muduo;
using namespace muduo::net;

EventLoopThread::EventLoopThread(const ThreadInitCallback& cb,
                                 const string& name)
  : loop_(nullptr),
    exiting_(false),
    callback_(cb),
    name_(name)
{
}

EventLoopThread::~EventLoopThread()
{
  exiting_ = true;
  if (loop_ != nullptr) // not 100% race-free, eg. threadFunc could be running callback_.
  {
    // still a tiny chance to call destructed object, if thread<PERSON>un<PERSON> exits just now.
    // but when EventLoopThread destructs, usually programming is exiting anyway.
    loop_->quit();
    if (thread_.joinable()) {
      thread_.join();
    }
  }
}

EventLoop* EventLoopThread::startLoop()
{
  assert(!thread_.joinable());
  thread_ = std::thread(&EventLoopThread::threadFunc, this);

  EventLoop* loop = nullptr;
  {
    std::unique_lock<std::mutex> lock(mutex_);
    while (loop_ == nullptr)
    {
      cond_.wait(lock);
    }
    loop = loop_;
  }

  return loop;
}

void EventLoopThread::threadFunc()
{
  EventLoop loop;

  if (callback_)
  {
    callback_(&loop);
  }

  {
    std::lock_guard<std::mutex> lock(mutex_);
    loop_ = &loop;
    cond_.notify_one();
  }

  loop.loop();
  //assert(exiting_);
  std::lock_guard<std::mutex> lock(mutex_);
  loop_ = nullptr;
}

