add_library(core SHARED)

# 添加源文件
file(GLOB_RECURSE CORE_SOURCES 
    "${CMAKE_CURRENT_SOURCE_DIR}/src/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/*.cc"
)

target_sources(core
    PRIVATE
        ${CORE_SOURCES}
)

# 设置包含目录
target_include_directories(core
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# 链接依赖库
target_link_libraries(core
    PUBLIC
        spdlog::spdlog
        nlohmann_json::nlohmann_json
        libuuid::libuuid
        boost::boost
        ${CMAKE_DL_LIBS}  # 添加动态库加载支持
) 