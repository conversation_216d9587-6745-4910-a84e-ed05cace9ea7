{"plugins": [{"id": 1, "description": "Message Plugin - <PERSON>les all messages", "library_path": "/root/zexuan/cpp/libs/plugins/libmessage_plugin.so", "enabled": true}, {"id": 2, "description": "filename Plugin - Handles comic book management and processing", "library_path": "/root/zexuan/cpp/libs/plugins/libfilename_plugin.so", "enabled": true}, {"id": 255, "description": "Summary Plugin - Summary plugin functionality", "library_path": "/root/zexuan/cpp/libs/plugins/libsummary_plugin.so", "enabled": true}]}