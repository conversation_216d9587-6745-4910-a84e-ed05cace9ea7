/**
 * @file main.cpp
 * @brief Automated plugin system with SystemInitializer
 * <AUTHOR> project
 * @date 2024
 */

#include "system_initializer.hpp"
#include "zexuan/base/singleton_registry.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/base/message.hpp"
#include "zexuan/logger.hpp"
#include <iostream>
#include <filesystem>

using namespace zexuan;

/**
 * @brief 测试插件系统的文件重命名功能
 * @param directoryPath 要处理的目录路径
 */
void testFileRenaming(const std::string& directoryPath) {
    auto logger = Logger::getFileLogger("main");
    logger->info("=== Testing File Renaming with Automated Plugin System ===");

    try {
        // 获取共享的 mediator 实例
        auto mediator = SingletonRegistry::getInstance().get<base::BaseMediator>();

        // 从配置文件加载消息
        base::Message renameMessage;
        const std::string configPath = "/root/zexuan/cpp/config/message";

        if (!renameMessage.loadFromHexFile(configPath)) {
            logger->error("Failed to load message from config file: {}", configPath);
            logger->info("Using default hardcoded message instead");

            return ;
        } else {
            logger->info("Successfully loaded message from config file: {}", configPath);
            logger->info("Message details - TYP: 0x{:02x}, VSQ: 0x{:02x}, COT: 0x{:02x}, SOURCE: 0x{:02x}, TARGET: 0x{:02x}, FUN: 0x{:02x}, INF: 0x{:02x}",
                        renameMessage.getTyp(), renameMessage.getVsq(), renameMessage.getCot(),
                        renameMessage.getSource(), renameMessage.getTarget(), renameMessage.getFun(), renameMessage.getInf());
        }

        // 将目录路径存储到消息的 variableStructure_ 中
        renameMessage.setTextContent(directoryPath);

        logger->info("Sending file rename request for directory: {}", directoryPath);

        // 发送消息给插件
        std::string description;
        int result = mediator->sendMessage(renameMessage, description);

        if (result == 0) {
            logger->info("Message sent successfully!");
            if (!description.empty()) {
                logger->info("  Description: {}", description);
            }
        } else {
            logger->error("Failed to send message, error code: {}", result);
            if (!description.empty()) {
                logger->error("  Error description: {}", description);
            }
        }

    } catch (const std::exception& e) {
        logger->error("Exception occurred during file renaming test: {}", e.what());
    }
}

int main(int argc, char* argv[]) {
    auto logger = Logger::getFileLogger("main");
    logger->info("=== Zexuan Automated Plugin System ===");

    try {
        // 1. 使用 SystemManager 进行 RAII 风格的系统管理
        SystemManager systemManager;
        logger->info("System initialized successfully");

        // 2. 解析命令行参数
        std::string directoryPath;
        if (argc >= 2) {
            directoryPath = argv[1];

            // 检查目录是否存在
            if (!std::filesystem::exists(directoryPath)) {
                logger->error("Directory '{}' does not exist.", directoryPath);
                return 1;
            }

            if (!std::filesystem::is_directory(directoryPath)) {
                logger->error("'{}' is not a directory.", directoryPath);
                return 1;
            }

            logger->info("Target directory: {}", directoryPath);

            // 3. 执行文件重命名测试
            testFileRenaming(directoryPath);

        } else {
            logger->info("No arguments provided. Using empty directory.");
            testFileRenaming("");
        }

        logger->info("Program completed successfully!");
        logger->info("System will automatically cleanup when exiting...");

    } catch (const std::exception& e) {
        logger->error("Program failed: {}", e.what());
        return 1;
    } catch (...) {
        logger->error("Program failed: Unknown exception");
        return 1;
    }

    return 0;
}