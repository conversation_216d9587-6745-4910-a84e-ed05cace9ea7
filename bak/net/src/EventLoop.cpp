#include "zexuan/net/EventLoop.hpp"
#include "zexuan/net/Channel.hpp"
#include "zexuan/net/EPoller.hpp"
#include "zexuan/net/TimerQueue.hpp"
#include "zexuan/logger.hpp"

#include <signal.h>
#include <sys/eventfd.h>
#include <unistd.h>
#include <sstream>
#include <cassert>

namespace zexuan::net {

namespace {
thread_local EventLoop* t_loopInThisThread = nullptr;
constexpr int kPollTimeMs = 10000;

/// Create an eventfd for waking up the event loop
[[nodiscard]] int createEventfd() {
  int evtfd = ::eventfd(0, EFD_NONBLOCK | EFD_CLOEXEC);
  if (evtfd < 0) {
    // Use static logger for global functions
    static auto logger = zexuan::Logger::getFileLogger("net/EventLoop");
    logger->error("Failed in eventfd");
    abort();
  }
  return evtfd;
}

/// RAII class to ignore SIGPIPE signal
class IgnoreSigPipe {
 public:
  IgnoreSigPipe() {
    ::signal(SIGPIPE, SIG_IGN);
  }
};

// Global instance to ignore SIGPIPE
[[maybe_unused]] static IgnoreSigPipe initObj;

} // anonymous namespace

EventLoop::EventLoop()
  : threadId_(std::this_thread::get_id()),
    poller_(std::make_unique<EPoller>(this)),
    timerQueue_(std::make_unique<TimerQueue>(this)),
    wakeupFd_(createEventfd()),
    wakeupChannel_(std::make_unique<Channel>(this, wakeupFd_)),
    eventloop_logger_(zexuan::Logger::getFileLogger("net/EventLoop"))
{
  std::stringstream ss;
  ss << threadId_;
  auto threadIdStr = ss.str();

  if (t_loopInThisThread) {
    eventloop_logger_->error("Another EventLoop exists in this thread {}", threadIdStr);
  } else {
    t_loopInThisThread = this;
  }

  wakeupChannel_->setReadCallback([this](Channel::TimePoint) { handleRead(); });
  wakeupChannel_->enableReading();
}

EventLoop::~EventLoop() {
  assert(!looping_.load(std::memory_order_relaxed));
  ::close(wakeupFd_);
  t_loopInThisThread = nullptr;
}

void EventLoop::loop() {
  assert(!looping_.load(std::memory_order_relaxed));
  assertInLoopThread();

  looping_.store(true, std::memory_order_relaxed);
  quit_.store(false, std::memory_order_relaxed);

  while (!quit_.load(std::memory_order_relaxed)) {
    activeChannels_.clear();
    pollReturnTime_ = poller_->poll(kPollTimeMs, &activeChannels_);

    // Handle active channels using range-based for loop
    for (auto* channel : activeChannels_) {
      channel->handleEvent(pollReturnTime_);
    }

    doPendingFunctors();
  }

  std::stringstream ss;
  ss << static_cast<void*>(this);
  eventloop_logger_->info("EventLoop {} stop looping", ss.str());

  looping_.store(false, std::memory_order_relaxed);
}

void EventLoop::quit() {
  quit_.store(true, std::memory_order_relaxed);
  if (!isInLoopThread()) {
    wakeup();
  }
}

void EventLoop::runInLoop(const Functor& cb) {
  if (isInLoopThread()) {
    cb();
  } else {
    queueInLoop(cb);
  }
}

void EventLoop::queueInLoop(const Functor& cb) {
  {
    std::lock_guard<std::mutex> lock(mutex_);
    pendingFunctors_.push_back(cb);
  }

  if (!isInLoopThread() || callingPendingFunctors_.load(std::memory_order_relaxed)) {
    wakeup();
  }
}

EventLoop::TimerHandle EventLoop::runAt(const TimePoint& time, const TimerCallback& cb) {
  return timerQueue_->addTimer(cb, time, Duration::zero());
}

EventLoop::TimerHandle EventLoop::runAfter(Duration delay, const TimerCallback& cb) {
  using namespace std::chrono;
  TimePoint time = system_clock::now() + delay;
  return runAt(time, cb);
}

EventLoop::TimerHandle EventLoop::runEvery(Duration interval, const TimerCallback& cb) {
  using namespace std::chrono;
  TimePoint time = system_clock::now() + interval;
  return timerQueue_->addTimer(cb, time, interval);
}

void EventLoop::cancel(const TimerHandle& timerId) {
  timerQueue_->cancel(timerId);
}

void EventLoop::updateChannel(Channel* channel) {
  assert(channel->ownerLoop() == this);
  assertInLoopThread();
  poller_->updateChannel(channel);
}

void EventLoop::removeChannel(Channel* channel) {
  assert(channel->ownerLoop() == this);
  assertInLoopThread();
  poller_->removeChannel(channel);
}

void EventLoop::abortNotInLoopThread() {
  std::stringstream ss1, ss2, ss3;
  ss1 << fmt::ptr(this);
  ss2 << threadId_;
  ss3 << std::this_thread::get_id();

  eventloop_logger_->error("EventLoop::abortNotInLoopThread - EventLoop {} was created in threadId_ = {}, current thread id = {}",
       ss1.str(), ss2.str(), ss3.str());
}

void EventLoop::wakeup() {
  constexpr uint64_t one = 1;
  ssize_t n = ::write(wakeupFd_, &one, sizeof(one));
  if (n != sizeof(one)) {
    eventloop_logger_->error("EventLoop::wakeup() writes {} bytes instead of 8", n);
  }
}

void EventLoop::handleRead() {
  uint64_t one = 1;
  ssize_t n = ::read(wakeupFd_, &one, sizeof(one));
  if (n != sizeof(one)) {
    eventloop_logger_->error("EventLoop::handleRead() reads {} bytes instead of 8", n);
  }
}

void EventLoop::doPendingFunctors() {
  std::vector<Functor> functors;
  callingPendingFunctors_.store(true, std::memory_order_relaxed);

  {
    std::lock_guard<std::mutex> lock(mutex_);
    functors.swap(pendingFunctors_);
  }

  // Use range-based for loop for better readability
  for (const auto& functor : functors) {
    functor();
  }

  callingPendingFunctors_.store(false, std::memory_order_relaxed);
}

} // namespace zexuan::net

