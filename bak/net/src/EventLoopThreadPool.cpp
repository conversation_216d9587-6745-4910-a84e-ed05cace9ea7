#include "zexuan/net/EventLoopThreadPool.hpp"
#include "zexuan/net/EventLoop.hpp"
#include "zexuan/net/EventLoopThread.hpp"
#include "zexuan/logger.hpp"

#include <cassert>

namespace zexuan::net {

// Static logger for EventLoopThreadPool
static auto thread_pool_logger_ = zexuan::Logger::getFileLogger("net/EventLoopThreadPool");

EventLoopThreadPool::EventLoopThreadPool(EventLoop* baseLoop, const std::string& nameArg)
  : baseLoop_(baseLoop),
    name_(nameArg)
{
  assert(baseLoop != nullptr);
  thread_pool_logger_->info("EventLoopThreadPool [{}] created", name_);
}

EventLoopThreadPool::~EventLoopThreadPool()
{
  // Don't delete loops, they are managed by EventLoopThread objects
  thread_pool_logger_->info("EventLoopThreadPool [{}] destroyed", name_);
}

void EventLoopThreadPool::start(const ThreadInitCallback& cb)
{
  assert(!started_.load());
  baseLoop_->assertInLoopThread();

  started_.store(true);

  for (int i = 0; i < numThreads_; ++i) {
    std::string threadName = name_ + std::to_string(i);
    auto thread = std::make_unique<EventLoopThread>(cb, threadName);
    EventLoop* loop = thread->startLoop();
    threads_.push_back(std::move(thread));
    loops_.push_back(loop);
  }

  if (numThreads_ == 0 && cb) {
    cb(baseLoop_);
  }

  thread_pool_logger_->info("EventLoopThreadPool [{}] started with {} threads",
                           name_, numThreads_);
}

EventLoop* EventLoopThreadPool::getNextLoop()
{
  baseLoop_->assertInLoopThread();
  assert(started_.load());
  EventLoop* loop = baseLoop_;

  if (!loops_.empty()) {
    // Round-robin selection
    int current = next_.fetch_add(1) % static_cast<int>(loops_.size());
    loop = loops_[current];
  }
  return loop;
}

EventLoop* EventLoopThreadPool::getLoopForHash(size_t hashCode)
{
  baseLoop_->assertInLoopThread();
  EventLoop* loop = baseLoop_;

  if (!loops_.empty()) {
    loop = loops_[hashCode % loops_.size()];
  }
  return loop;
}

std::vector<EventLoop*> EventLoopThreadPool::getAllLoops()
{
  baseLoop_->assertInLoopThread();
  assert(started_.load());

  if (loops_.empty()) {
    return std::vector<EventLoop*>{baseLoop_};
  } else {
    return loops_;
  }
}

} // namespace zexuan::net
