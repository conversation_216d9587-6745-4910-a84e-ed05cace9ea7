#include "zexuan/net/Acceptor.hpp"
#include "zexuan/net/EventLoop.hpp"
#include "zexuan/net/InetAddress.hpp"
#include "zexuan/net/SocketsOps.hpp"
#include "zexuan/logger.hpp"

#include <cerrno>
#include <fcntl.h>
#include <unistd.h>
#include <cassert>

namespace zexuan::net {

// Static logger for Acceptor
static auto acceptor_logger_ = zexuan::Logger::getFileLogger("net/Acceptor");

Acceptor::Acceptor(EventLoop* loop, const InetAddress& listenAddr, bool reuseport)
  : loop_(loop),
    acceptSocket_(sockets::createNonblockingOrDie(AF_INET)),
    acceptChannel_(loop, acceptSocket_.fd()),
    idleFd_(::open("/dev/null", O_RDONLY | O_CLOEXEC))
{
  assert(idleFd_ >= 0);
  acceptSocket_.setReuseAddr(true);
  // Note: setReusePort not available in current Socket implementation
  acceptSocket_.bindAddress(listenAddr);

  // Set up read callback using lambda
  acceptChannel_.setReadCallback([this](TimePoint) {
    handleRead();
  });

  acceptor_logger_->info("Acceptor created for {}", listenAddr.toIpPort());
}

Acceptor::~Acceptor()
{
  acceptChannel_.disableAll();
  ::close(idleFd_);
  acceptor_logger_->info("Acceptor destroyed");
}

void Acceptor::listen()
{
  loop_->assertInLoopThread();
  listening_ = true;
  acceptSocket_.listen();
  acceptChannel_.enableReading();
  acceptor_logger_->info("Acceptor started listening");
}

void Acceptor::handleRead()
{
  loop_->assertInLoopThread();
  struct sockaddr_in peerAddr;
  socklen_t addrlen = sizeof(peerAddr);

  // Accept new connection
  int connfd = ::accept(acceptSocket_.fd(),
                       reinterpret_cast<struct sockaddr*>(&peerAddr),
                       &addrlen);
  if (connfd >= 0) {
    InetAddress peerInetAddr(peerAddr);
    acceptor_logger_->debug("Accepted connection from {}", peerInetAddr.toIpPort());

    if (newConnectionCallback_) {
      newConnectionCallback_(connfd, peerInetAddr);
    } else {
      acceptor_logger_->warn("No new connection callback set, closing connection");
      sockets::close(connfd);
    }
  } else {
    acceptor_logger_->error("Accept failed: {}", strerror(errno));

    // Handle EMFILE (too many open files) error
    // This is a technique from libev to handle the case when we can't accept
    // more connections due to file descriptor limits
    if (errno == EMFILE) {
      ::close(idleFd_);
      idleFd_ = ::accept(acceptSocket_.fd(), nullptr, nullptr);
      ::close(idleFd_);
      idleFd_ = ::open("/dev/null", O_RDONLY | O_CLOEXEC);
      acceptor_logger_->warn("EMFILE error handled using idle fd technique");
    }
  }
}

} // namespace zexuan::net

