#include "zexuan/net/TcpServer.hpp"
#include "zexuan/net/Acceptor.hpp"
#include "zexuan/net/EventLoop.hpp"
#include "zexuan/net/EventLoopThreadPool.hpp"
#include "zexuan/net/SocketsOps.hpp"
#include "zexuan/logger.hpp"

#include <functional>
#include <cassert>

namespace zexuan::net {

// Static logger for TcpServer
static auto tcp_server_logger_ = zexuan::Logger::getFileLogger("net/TcpServer");

TcpServer::TcpServer(EventLoop* loop,
                     const InetAddress& listenAddr,
                     const std::string& nameArg,
                     Option option)
  : loop_(loop),
    ipPort_(listenAddr.toIpPort()),
    name_(nameArg),
    acceptor_(std::make_unique<Acceptor>(loop, listenAddr, option == Option::ReusePort)),
    threadPool_(std::make_shared<EventLoopThreadPool>(loop, name_))
{
  assert(loop != nullptr);

  // Set up acceptor callback
  acceptor_->setNewConnectionCallback(
      [this](int sockfd, const InetAddress& peerAddr) {
        newConnection(sockfd, peerAddr);
      });

  tcp_server_logger_->info("TcpServer [{}] listening on {}", name_, ipPort_);
}

TcpServer::~TcpServer()
{
  loop_->assertInLoopThread();
  tcp_server_logger_->info("TcpServer [{}] destructing", name_);

  // Clean up all connections
  for (auto& [name, conn] : connections_) {
    TcpConnectionPtr connection = conn;
    conn.reset();
    connection->getLoop()->runInLoop([connection]() {
      connection->connectDestroyed();
    });
  }
}

void TcpServer::setThreadNum(int numThreads)
{
  assert(numThreads >= 0);
  threadPool_->setThreadNum(numThreads);
}

void TcpServer::start()
{
  bool expected = false;
  if (started_.compare_exchange_strong(expected, true)) {
    threadPool_->start(threadInitCallback_);

    assert(!acceptor_->listening());
    loop_->runInLoop([this]() {
      acceptor_->listen();
    });

    tcp_server_logger_->info("TcpServer [{}] started", name_);
  }
}

void TcpServer::newConnection(int sockfd, const InetAddress& peerAddr)
{
  loop_->assertInLoopThread();
  EventLoop* ioLoop = threadPool_->getNextLoop();

  // Generate unique connection name
  std::string connName = name_ + "-" + ipPort_ + "#" + std::to_string(nextConnId_);
  ++nextConnId_;

  tcp_server_logger_->info("TcpServer [{}] - new connection [{}] from {}",
                          name_, connName, peerAddr.toIpPort());

  InetAddress localAddr(sockets::getLocalAddr(sockfd));

  // Create new connection using make_shared for better performance
  auto conn = std::make_shared<TcpConnection>(ioLoop, connName, sockfd, localAddr, peerAddr);

  connections_[connName] = conn;

  // Set up callbacks
  conn->setConnectionCallback(connectionCallback_);
  conn->setMessageCallback(messageCallback_);
  conn->setWriteCompleteCallback(writeCompleteCallback_);
  conn->setCloseCallback([this](const TcpConnectionPtr& connection) {
    removeConnection(connection);
  });

  // Establish connection in IO loop
  ioLoop->runInLoop([conn]() {
    conn->connectEstablished();
  });
}

void TcpServer::removeConnection(const TcpConnectionPtr& conn)
{
  // Thread safe - delegate to loop thread
  loop_->runInLoop([this, conn]() {
    removeConnectionInLoop(conn);
  });
}

void TcpServer::removeConnectionInLoop(const TcpConnectionPtr& conn)
{
  loop_->assertInLoopThread();
  tcp_server_logger_->info("TcpServer [{}] - removing connection [{}]",
                          name_, conn->name());

  size_t n = connections_.erase(conn->name());
  assert(n == 1);
  (void)n;  // Suppress unused variable warning in release builds

  EventLoop* ioLoop = conn->getLoop();
  ioLoop->queueInLoop([conn]() {
    conn->connectDestroyed();
  });
}

} // namespace zexuan::net

