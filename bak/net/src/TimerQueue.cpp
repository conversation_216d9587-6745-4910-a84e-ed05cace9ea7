#define __STDC_LIMIT_MACROS
#include "zexuan/net/TimerQueue.hpp"
#include "zexuan/net/EventLoop.hpp"
#include "zexuan/net/Timer.hpp"
#include "zexuan/logger.hpp"

#include <sys/timerfd.h>
#include <unistd.h>
#include <cassert>

namespace zexuan::net::detail {

using TimePoint = Timer::TimePoint;
using Duration = Timer::Duration;

// Static logger for TimerQueue detail functions
static auto timerqueue_detail_logger_ = zexuan::Logger::getFileLogger("net/TimerQueue");

/// Create a timerfd for the timer queue
[[nodiscard]] int createTimerfd() {
  int timerfd = ::timerfd_create(CLOCK_MONOTONIC, TFD_NONBLOCK | TFD_CLOEXEC);
  if (timerfd < 0) {
    timerqueue_detail_logger_->error("Failed in timerfd_create");
  }
  return timerfd;
}

/// Calculate timespec from now until the given time point
[[nodiscard]] timespec howMuchTimeFromNow(TimePoint when) {
  using namespace std::chrono;

  auto now = system_clock::now();
  auto duration = when - now;
  auto microseconds = duration_cast<std::chrono::microseconds>(duration).count();

  // Ensure minimum delay to avoid busy waiting
  constexpr int64_t kMinDelayMicroseconds = 100;
  if (microseconds < kMinDelayMicroseconds) {
    microseconds = kMinDelayMicroseconds;
  }

  struct timespec ts;
  ts.tv_sec = static_cast<time_t>(microseconds / 1'000'000);
  ts.tv_nsec = static_cast<long>((microseconds % 1'000'000) * 1'000);
  return ts;
}

/// Read from timerfd to acknowledge timer expiration
void readTimerfd(int timerfd, TimePoint now) {
  uint64_t howmany;
  ssize_t n = ::read(timerfd, &howmany, sizeof(howmany));

  timerqueue_detail_logger_->debug("TimerQueue::handleRead() {} at {}", howmany,
       std::chrono::system_clock::to_time_t(now));

  if (n != sizeof(howmany)) {
    timerqueue_detail_logger_->error("TimerQueue::handleRead() reads {} bytes instead of 8", n);
  }
}

/// Reset timerfd to fire at the given expiration time
void resetTimerfd(int timerfd, TimePoint expiration) {
  struct itimerspec newValue{};
  struct itimerspec oldValue{};

  newValue.it_value = howMuchTimeFromNow(expiration);

  int ret = ::timerfd_settime(timerfd, 0, &newValue, &oldValue);
  if (ret) {
    timerqueue_detail_logger_->error("timerfd_settime() failed");
  }
}

} // namespace zexuan::net::detail

namespace zexuan::net {

using namespace detail;

TimerQueue::TimerQueue(EventLoop* loop)
  : loop_(loop),
    timerfd_(createTimerfd()),
    timerfdChannel_(loop, timerfd_),
    timerqueue_logger_(zexuan::Logger::getFileLogger("net/TimerQueue"))
{
  timerfdChannel_.setReadCallback([this](Channel::TimePoint) { handleRead(); });
  timerfdChannel_.enableReading();
}

TimerQueue::~TimerQueue() {
  ::close(timerfd_);
}

TimerWeakPtr TimerQueue::addTimer(const TimerCallback& cb,
                                 TimePoint when,
                                 Duration interval) {
  auto timer = std::make_shared<Timer>(cb, when, interval);
  loop_->runInLoop([this, timer]() { addTimerInLoop(timer); });
  return timer;
}

void TimerQueue::cancel(const TimerWeakPtr& timerId) {
  loop_->runInLoop([this, timerId]() { cancelInLoop(timerId); });
}

void TimerQueue::addTimerInLoop(TimerPtr timer) {
  loop_->assertInLoopThread();
  bool earliestChanged = insert(timer);

  if (earliestChanged) {
    resetTimerfd(timerfd_, timer->expiration());
  }
}

void TimerQueue::cancelInLoop(const TimerWeakPtr& timerId) {
  loop_->assertInLoopThread();

  if (auto timer = timerId.lock()) {
    Entry entry{timer->expiration(), timer};
    if (auto it = timers_.find(entry); it != timers_.end()) {
      [[maybe_unused]] size_t n = timers_.erase(entry);
      assert(n == 1);
    }
  }
}

void TimerQueue::handleRead() {
  loop_->assertInLoopThread();

  TimePoint now = std::chrono::system_clock::now();
  readTimerfd(timerfd_, now);

  auto expired = getExpired(now);

  // Execute expired timers
  callingExpiredTimers_.store(true, std::memory_order_relaxed);
  for (const auto& [timepoint, timer] : expired) {
    timer->run();
  }
  callingExpiredTimers_.store(false, std::memory_order_relaxed);

  reset(expired, now);
}

std::vector<TimerQueue::Entry> TimerQueue::getExpired(TimePoint now) {
  std::vector<Entry> expired;

  // Create sentinel entry to find all timers that should expire by now
  Entry sentry{now, std::shared_ptr<Timer>()};
  auto end_it = timers_.lower_bound(sentry);

  assert(end_it == timers_.end() || now < end_it->first);

  // Move expired timers to result vector
  expired.reserve(std::distance(timers_.begin(), end_it));
  std::ranges::copy(timers_.begin(), end_it, std::back_inserter(expired));

  // Remove expired timers from the queue
  timers_.erase(timers_.begin(), end_it);

  return expired;
}

void TimerQueue::reset(const std::vector<Entry>& expired, TimePoint now) {
  TimePoint nextExpire{};

  // Restart repeating timers
  for (const auto& [timepoint, timer] : expired) {
    if (timer->repeat()) {
      timer->restart(now);
      [[maybe_unused]] bool inserted = insert(timer);
    }
  }

  // Set timerfd for next expiration
  if (!timers_.empty()) {
    nextExpire = timers_.begin()->second->expiration();
  }

  if (nextExpire != TimePoint{}) {
    resetTimerfd(timerfd_, nextExpire);
  }
}

bool TimerQueue::insert(TimerPtr timer) {
  loop_->assertInLoopThread();

  TimePoint when = timer->expiration();
  bool earliestChanged = timers_.empty() || when < timers_.begin()->first;

  timers_.emplace(when, timer);
  return earliestChanged;
}

} // namespace zexuan::net

