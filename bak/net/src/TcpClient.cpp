#include "zexuan/net/TcpClient.hpp"
#include "zexuan/net/Connector.hpp"
#include "zexuan/net/EventLoop.hpp"
#include "zexuan/net/SocketsOps.hpp"
#include "zexuan/logger.hpp"

#include <cassert>
#include <cstdio>

namespace zexuan::net {

// Static logger for TcpClient
static auto tcp_client_logger_ = zexuan::Logger::getFileLogger("net/TcpClient");

namespace detail {

void removeConnection(EventLoop* loop, const TcpConnectionPtr& conn)
{
  loop->queueInLoop([conn]() {
    conn->connectDestroyed();
  });
}

} // namespace detail

TcpClient::TcpClient(EventLoop* loop,
                     const InetAddress& serverAddr,
                     const std::string& nameArg)
  : loop_(loop),
    connector_(std::make_unique<Connector>(loop, serverAddr)),
    name_(nameArg),
    connectionCallback_(defaultConnectionCallback),
    messageCallback_(defaultMessageCallback)
{
  assert(loop != nullptr);

  connector_->setNewConnectionCallback([this](int sockfd) {
    newConnection(sockfd);
  });

  tcp_client_logger_->info("TcpClient [{}] created with connector", name_);
}

TcpClient::~TcpClient()
{
  tcp_client_logger_->info("TcpClient [{}] destructor called", name_);

  TcpConnectionPtr conn;
  bool unique = false;
  {
    std::lock_guard<std::mutex> lock(mutex_);
    unique = connection_.unique();
    conn = connection_;
  }

  if (conn) {
    assert(loop_ == conn->getLoop());

    // Set close callback to clean up connection
    CloseCallback cb = [this](const TcpConnectionPtr& c) {
      detail::removeConnection(loop_, c);
    };

    loop_->runInLoop([conn, cb]() {
      conn->setCloseCallback(cb);
    });

    if (unique) {
      conn->shutdown();
    }
  } else {
    connector_->stop();
  }
}

void TcpClient::connect()
{
  tcp_client_logger_->info("TcpClient [{}] connecting to {}",
                          name_, connector_->serverAddress().toIpPort());
  connect_.store(true);
  connector_->start();
}

void TcpClient::disconnect()
{
  connect_.store(false);

  {
    std::lock_guard<std::mutex> lock(mutex_);
    if (connection_) {
      connection_->shutdown();
    }
  }
}

void TcpClient::stop()
{
  connect_.store(false);
  connector_->stop();
}

void TcpClient::newConnection(int sockfd)
{
  loop_->assertInLoopThread();
  InetAddress peerAddr(sockets::getPeerAddr(sockfd));

  std::string connName = name_ + ":" + peerAddr.toIpPort() + "#" + std::to_string(nextConnId_);
  ++nextConnId_;

  InetAddress localAddr(sockets::getLocalAddr(sockfd));

  // Create new connection using make_shared
  TcpConnectionPtr conn = std::make_shared<TcpConnection>(
      loop_, connName, sockfd, localAddr, peerAddr);

  conn->setConnectionCallback(connectionCallback_);
  conn->setMessageCallback(messageCallback_);
  conn->setWriteCompleteCallback(writeCompleteCallback_);
  conn->setCloseCallback([this](const TcpConnectionPtr& c) {
    removeConnection(c);
  });

  {
    std::lock_guard<std::mutex> lock(mutex_);
    connection_ = conn;
  }

  conn->connectEstablished();
  tcp_client_logger_->info("TcpClient [{}] connection established: {}",
                          name_, connName);
}

void TcpClient::removeConnection(const TcpConnectionPtr& conn)
{
  loop_->assertInLoopThread();
  assert(loop_ == conn->getLoop());

  {
    std::lock_guard<std::mutex> lock(mutex_);
    assert(connection_ == conn);
    connection_.reset();
  }

  loop_->queueInLoop([conn]() {
    conn->connectDestroyed();
  });

  if (retry_.load() && connect_.load()) {
    tcp_client_logger_->info("TcpClient [{}] reconnecting to {}",
                            name_, connector_->serverAddress().toIpPort());
    connector_->restart();
  }
}

} // namespace zexuan::net

