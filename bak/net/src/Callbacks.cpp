#include "zexuan/net/Callbacks.hpp"
#include "zexuan/net/TcpConnection.hpp"
#include "zexuan/net/Buffer.hpp"
#include "zexuan/logger.hpp"

namespace zexuan::net {

// Static logger for callbacks
static auto callbacks_logger_ = zexuan::Logger::getFileLogger("net/Callbacks");

void defaultConnectionCallback(const TcpConnectionPtr& conn)
{
    callbacks_logger_->info("Connection {}: {} -> {} is {}",
                           conn->name(),
                           conn->localAddress().toIpPort(),
                           conn->peerAddress().toIpPort(),
                           conn->connected() ? "UP" : "DOWN");
}

void defaultMessageCallback(const TcpConnectionPtr& conn, Buffer* buffer, TimePoint receiveTime)
{
    // Simply discard the message
    buffer->retrieveAll();
    callbacks_logger_->debug("Default message callback: discarded {} bytes from {}",
                            buffer->readableBytes(), conn->name());
}

} // namespace zexuan::net
