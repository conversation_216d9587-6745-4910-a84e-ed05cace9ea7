#include "zexuan/net/Buffer.hpp"
#include "zexuan/net/SocketsOps.hpp"
#include "zexuan/logger.hpp"

#include <errno.h>
#include <sys/uio.h>

namespace zexuan::net {

// Static logger for Buffer class
static auto buffer_logger_ = zexuan::Logger::getFileLogger("net/Buffer");

ssize_t Buffer::readFd(int fd, int* savedErrno) {
  // Use stack buffer for extra space to avoid frequent reallocations
  constexpr size_t kExtraBufferSize = 65536;
  char extrabuf[kExtraBufferSize];

  struct iovec vec[2];
  const size_t writable = writableBytes();

  // First vector points to writable area in buffer
  vec[0].iov_base = data() + writerIndex_;
  vec[0].iov_len = writable;

  // Second vector points to stack buffer
  vec[1].iov_base = extrabuf;
  vec[1].iov_len = kExtraBufferSize;

  // Use readv to read into both buffers atomically
  const ssize_t n = ::readv(fd, vec, 2);

  if (n < 0) {
    *savedErrno = errno;
  } else if (static_cast<size_t>(n) <= writable) {
    // All data fit in the main buffer
    writerIndex_ += static_cast<size_t>(n);
  } else {
    // Data overflowed to extra buffer
    writerIndex_ = buffer_.size();
    append(extrabuf, static_cast<size_t>(n) - writable);
  }

  return n;
}

} // namespace zexuan::net

