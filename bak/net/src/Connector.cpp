#include "zexuan/net/Connector.hpp"
#include "zexuan/net/Channel.hpp"
#include "zexuan/net/EventLoop.hpp"
#include "zexuan/net/SocketsOps.hpp"
#include "zexuan/logger.hpp"

#include <cerrno>
#include <cassert>

namespace zexuan::net {

// Static logger for Connector
static auto connector_logger_ = zexuan::Logger::getFileLogger("net/Connector");

Connector::Connector(EventLoop* loop, const InetAddress& serverAddr)
  : loop_(loop),
    serverAddr_(serverAddr)
{
  assert(loop != nullptr);
  connector_logger_->debug("Connector created for {}", serverAddr.toIpPort());
}

Connector::~Connector()
{
  connector_logger_->debug("Connector destroyed");
  assert(!channel_);
}

void Connector::start()
{
  connect_.store(true);
  loop_->runInLoop([this]() {
    startInLoop();
  });
}

void Connector::startInLoop()
{
  loop_->assertInLoopThread();
  assert(state_.load() == States::kDisconnected);
  if (connect_.load()) {
    connect();
  } else {
    connector_logger_->debug("Connect cancelled, not connecting");
  }
}

void Connector::stop()
{
  connect_.store(false);
  loop_->queueInLoop([this]() {
    stopInLoop();
  });
}

void Connector::stopInLoop()
{
  loop_->assertInLoopThread();
  if (state_.load() == States::kConnecting) {
    setState(States::kDisconnected);
    int sockfd = removeAndResetChannel();
    retry(sockfd);
  }
}

void Connector::connect()
{
  int sockfd = sockets::createNonblockingOrDie(serverAddr_.family());
  int ret = sockets::connect(sockfd, serverAddr_.getSockAddrInet());
  int savedErrno = (ret == 0) ? 0 : errno;
  switch (savedErrno) {
    case 0:
    case EINPROGRESS:
    case EINTR:
    case EISCONN:
      connecting(sockfd);
      break;

    case EAGAIN:
    case EADDRINUSE:
    case EADDRNOTAVAIL:
    case ECONNREFUSED:
    case ENETUNREACH:
      retry(sockfd);
      break;

    case EACCES:
    case EPERM:
    case EAFNOSUPPORT:
    case EALREADY:
    case EBADF:
    case EFAULT:
    case ENOTSOCK:
      connector_logger_->error("Connect error: {}", std::strerror(savedErrno));
      sockets::close(sockfd);
      break;

    default:
      connector_logger_->error("Unexpected connect error: {}", std::strerror(savedErrno));
      sockets::close(sockfd);
      break;
  }
}

void Connector::restart()
{
  loop_->assertInLoopThread();
  setState(States::kDisconnected);
  retryDelayMs_ = kInitRetryDelayMs;
  connect_.store(true);
  startInLoop();
}

void Connector::connecting(int sockfd)
{
  setState(States::kConnecting);
  assert(!channel_);
  channel_ = std::make_unique<Channel>(loop_, sockfd);

  channel_->setWriteCallback([this]() {
    handleWrite();
  });
  channel_->setErrorCallback([this]() {
    handleError();
  });

  channel_->enableWriting();
}

int Connector::removeAndResetChannel()
{
  channel_->disableAll();
  channel_->remove();
  int sockfd = channel_->fd();
  // Can't reset channel_ here, because we are inside Channel::handleEvent
  loop_->queueInLoop([this]() {
    resetChannel();
  });
  return sockfd;
}

void Connector::resetChannel()
{
  channel_.reset();
}

void Connector::handleWrite()
{
  connector_logger_->trace("Connector::handleWrite state={}",
                          static_cast<int>(state_.load()));

  if (state_.load() == States::kConnecting) {
    int sockfd = removeAndResetChannel();
    int err = sockets::getSocketError(sockfd);
    if (err) {
      connector_logger_->warn("Connection failed with SO_ERROR = {}: {}",
                             err, std::strerror(err));
      retry(sockfd);
    } else if (sockets::isSelfConnect(sockfd)) {
      connector_logger_->warn("Self connect detected");
      retry(sockfd);
    } else {
      setState(States::kConnected);
      if (connect_.load()) {
        newConnectionCallback_(sockfd);
      } else {
        sockets::close(sockfd);
      }
    }
  } else {
    // Unexpected state
    assert(state_.load() == States::kDisconnected);
  }
}

void Connector::handleError()
{
  connector_logger_->error("Connector::handleError state={}",
                          static_cast<int>(state_.load()));
  if (state_.load() == States::kConnecting) {
    int sockfd = removeAndResetChannel();
    int err = sockets::getSocketError(sockfd);
    connector_logger_->trace("SO_ERROR = {}: {}", err, std::strerror(err));
    retry(sockfd);
  }
}

void Connector::retry(int sockfd)
{
  sockets::close(sockfd);
  setState(States::kDisconnected);
  if (connect_.load()) {
    connector_logger_->info("Retry connecting to {} in {} milliseconds",
                           serverAddr_.toIpPort(), retryDelayMs_);

    [[maybe_unused]] auto timer = loop_->runAfter(std::chrono::milliseconds(retryDelayMs_), [self = shared_from_this()]() {
      self->startInLoop();
    });

    retryDelayMs_ = std::min(retryDelayMs_ * 2, kMaxRetryDelayMs);
  } else {
    connector_logger_->debug("Connect cancelled, not retrying");
  }
}

} // namespace zexuan::net

