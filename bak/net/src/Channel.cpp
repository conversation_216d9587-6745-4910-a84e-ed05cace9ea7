#include "zexuan/net/Channel.hpp"
#include "zexuan/net/EventLoop.hpp"

#include <poll.h>
#include <cassert>

namespace zexuan::net {

Channel::Channel(EventLoop* loop, int fd)
  : loop_(loop),
    fd_(fd),
    events_(0),
    revents_(0),
    index_(-1),
    eventHandling_(false),
    channel_logger_(zexuan::Logger::getFileLogger("net/Channel"))
{
}

Channel::~Channel() {
  assert(!eventHandling_);
}

void Channel::update() {
  loop_->updateChannel(this);
}

void Channel::remove() {
  assert(isNoneEvent());
  loop_->removeChannel(this);
}

void Channel::handleEvent(TimePoint receiveTime) {
  eventHandling_ = true;

  // Handle invalid file descriptor
  if (revents_ & POLLNVAL) {
    channel_logger_->error("Channel::handleEvent() POLLNVAL for fd {}", fd_);
  }

  // Handle hangup (connection closed by peer)
  if ((revents_ & POLLHUP) && !(revents_ & POLLIN)) {
    channel_logger_->warn("Channel::handleEvent() POLLHUP for fd {}", fd_);
    if (closeCallback_) {
      closeCallback_();
    }
  }

  // Handle errors
  if (revents_ & (POLLERR | POLLNVAL)) {
    if (errorCallback_) {
      errorCallback_();
    }
  }

  // Handle read events
  if (revents_ & (POLLIN | POLLPRI | POLLRDHUP)) {
    if (readCallback_) {
      readCallback_(receiveTime);
    }
  }

  // Handle write events
  if (revents_ & POLLOUT) {
    if (writeCallback_) {
      writeCallback_();
    }
  }

  eventHandling_ = false;
}

} // namespace zexuan::net
