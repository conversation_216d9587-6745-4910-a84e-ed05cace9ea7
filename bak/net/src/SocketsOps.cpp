#include "zexuan/net/SocketsOps.hpp"
#include "zexuan/logger.hpp"

#include <errno.h>
#include <fcntl.h>
#include <stdio.h>
#include <strings.h>
#include <sys/socket.h>
#include <unistd.h>
#include <system_error>
#include <cstring>

namespace zexuan::net::sockets {

namespace {

using SA = struct sockaddr;

// Static logger for SocketsOps
static auto socketsops_logger_ = zexuan::Logger::getFileLogger("net/SocketsOps");

/// Safe cast from sockaddr_in to sockaddr
[[nodiscard]] constexpr const SA* sockaddr_cast(const struct sockaddr_in* addr) noexcept {
  return static_cast<const SA*>(static_cast<const void*>(addr));
}

/// Safe cast from sockaddr_in to sockaddr (non-const version)
[[nodiscard]] constexpr SA* sockaddr_cast(struct sockaddr_in* addr) noexcept {
  return static_cast<SA*>(static_cast<void*>(addr));
}

/// Set socket to non-blocking and close-on-exec
/// @throws std::system_error on failure
void setNonBlockAndCloseOnExec(int sockfd) {
  // Set non-blocking
  int flags = ::fcntl(sockfd, F_GETFL, 0);
  if (flags == -1) {
    throw std::system_error(errno, std::system_category(), "fcntl F_GETFL failed");
  }

  flags |= O_NONBLOCK;
  if (::fcntl(sockfd, F_SETFL, flags) == -1) {
    throw std::system_error(errno, std::system_category(), "fcntl F_SETFL failed");
  }

  // Set close-on-exec
  flags = ::fcntl(sockfd, F_GETFD, 0);
  if (flags == -1) {
    throw std::system_error(errno, std::system_category(), "fcntl F_GETFD failed");
  }

  flags |= FD_CLOEXEC;
  if (::fcntl(sockfd, F_SETFD, flags) == -1) {
    throw std::system_error(errno, std::system_category(), "fcntl F_SETFD failed");
  }
}

} // anonymous namespace

int createNonblockingOrDie(sa_family_t family) {
  int sockfd = ::socket(family, SOCK_STREAM | SOCK_NONBLOCK | SOCK_CLOEXEC, IPPROTO_TCP);
  if (sockfd < 0) {
    throw std::system_error(errno, std::system_category(), "socket creation failed");
  }
  return sockfd;
}

void bindOrDie(int sockfd, const struct sockaddr_in& addr) {
  int ret = ::bind(sockfd, sockaddr_cast(&addr), sizeof(struct sockaddr_in));
  if (ret < 0) {
    throw std::system_error(errno, std::system_category(), "bind failed");
  }
}

void listenOrDie(int sockfd) {
  int ret = ::listen(sockfd, SOMAXCONN);
  if (ret < 0) {
    throw std::system_error(errno, std::system_category(), "listen failed");
  }
}

int connect(int sockfd, const struct sockaddr_in& addr) noexcept {
  return ::connect(sockfd, sockaddr_cast(&addr), sizeof(struct sockaddr_in));
}

int accept(int sockfd, struct sockaddr_in* addr) noexcept {
  socklen_t addrlen = sizeof(struct sockaddr_in);
  int connfd = ::accept4(sockfd, sockaddr_cast(addr), &addrlen, SOCK_NONBLOCK | SOCK_CLOEXEC);

  if (connfd < 0) {
    int savedErrno = errno;

    // Log different types of errors appropriately
    switch (savedErrno) {
      // Expected/recoverable errors
      case EAGAIN:
      case ECONNABORTED:
      case EINTR:
      case EPROTO:
      case EPERM:
      case EMFILE:
        socketsops_logger_->debug("Socket::accept recoverable error: {}", std::strerror(savedErrno));
        errno = savedErrno;
        break;

      // Unexpected/serious errors
      case EBADF:
      case EFAULT:
      case EINVAL:
      case ENFILE:
      case ENOBUFS:
      case ENOMEM:
      case ENOTSOCK:
      case EOPNOTSUPP:
        socketsops_logger_->error("unexpected error of ::accept: {}", std::strerror(savedErrno));
        break;

      default:
        socketsops_logger_->error("unknown error of ::accept: {}", std::strerror(savedErrno));
        break;
    }
  }

  return connfd;
}

ssize_t sockets::read(int sockfd, void *buf, size_t count)
{
  return ::read(sockfd, buf, count);
}

ssize_t sockets::readv(int sockfd, const struct iovec *iov, int iovcnt)
{
  return ::readv(sockfd, iov, iovcnt);
}

ssize_t sockets::write(int sockfd, const void *buf, size_t count)
{
  return ::write(sockfd, buf, count);
}

void close(int sockfd) noexcept {
  if (::close(sockfd) < 0) {
    socketsops_logger_->error("sockets::close failed: {}", std::strerror(errno));
  }
}

void shutdownWrite(int sockfd) noexcept {
  if (::shutdown(sockfd, SHUT_WR) < 0) {
    socketsops_logger_->error("sockets::shutdownWrite failed: {}", std::strerror(errno));
  }
}

void sockets::toIpPort(char* buf, size_t size,
                       const struct sockaddr* addr)
{
  if (addr->sa_family == AF_INET6)
  {
    buf[0] = '[';
    toIp(buf+1, size-1, addr);
    size_t end = ::strlen(buf);
    const struct sockaddr_in6* addr6 = sockaddr_in6_cast(addr);
    uint16_t port = sockets::networkToHost16(addr6->sin6_port);
    assert(size > end);
    snprintf(buf+end, size-end, "]:%u", port);
    return;
  }
  toIp(buf, size, addr);
  size_t end = ::strlen(buf);
  const struct sockaddr_in* addr4 = sockaddr_in_cast(addr);
  uint16_t port = sockets::networkToHost16(addr4->sin_port);
  assert(size > end);
  snprintf(buf+end, size-end, ":%u", port);
}

void sockets::toIp(char* buf, size_t size,
                   const struct sockaddr* addr)
{
  if (addr->sa_family == AF_INET)
  {
    assert(size >= INET_ADDRSTRLEN);
    const struct sockaddr_in* addr4 = sockaddr_in_cast(addr);
    ::inet_ntop(AF_INET, &addr4->sin_addr, buf, static_cast<socklen_t>(size));
  }
  else if (addr->sa_family == AF_INET6)
  {
    assert(size >= INET6_ADDRSTRLEN);
    const struct sockaddr_in6* addr6 = sockaddr_in6_cast(addr);
    ::inet_ntop(AF_INET6, &addr6->sin6_addr, buf, static_cast<socklen_t>(size));
  }
}

void fromIpPort(const char* ip, uint16_t port, struct sockaddr_in* addr) noexcept {
  addr->sin_family = AF_INET;
  addr->sin_port = hostToNetwork16(port);
  if (::inet_pton(AF_INET, ip, &addr->sin_addr) <= 0) {
    socketsops_logger_->error("sockets::fromIpPort failed for {}:{}", ip, port);
  }
}

void fromIpPort(const char* ip, uint16_t port, struct sockaddr_in6* addr) noexcept {
  addr->sin_family = AF_INET6;
  addr->sin_port = hostToNetwork16(port);
  if (::inet_pton(AF_INET6, ip, &addr->sin6_addr) <= 0) {
    socketsops_logger_->error("sockets::fromIpPort failed for {}:{}", ip, port);
  }
}

struct sockaddr_in getLocalAddr(int sockfd) noexcept {
  struct sockaddr_in localaddr{};  // Zero-initialize using {}
  socklen_t addrlen = sizeof(localaddr);
  if (::getsockname(sockfd, sockaddr_cast(&localaddr), &addrlen) < 0) {
    socketsops_logger_->error("sockets::getLocalAddr failed: {}", std::strerror(errno));
  }
  return localaddr;
}

struct sockaddr_in getPeerAddr(int sockfd) noexcept {
  struct sockaddr_in peeraddr{};  // Zero-initialize using {}
  socklen_t addrlen = sizeof(peeraddr);
  if (::getpeername(sockfd, sockaddr_cast(&peeraddr), &addrlen) < 0) {
    socketsops_logger_->error("sockets::getPeerAddr failed: {}", std::strerror(errno));
  }
  return peeraddr;
}

int getSocketError(int sockfd) noexcept {
  int optval;
  socklen_t optlen = sizeof(optval);

  if (::getsockopt(sockfd, SOL_SOCKET, SO_ERROR, &optval, &optlen) < 0) {
    return errno;
  }
  return optval;
}

bool sockets::isSelfConnect(int sockfd)
{
  struct sockaddr_in6 localaddr = getLocalAddr(sockfd);
  struct sockaddr_in6 peeraddr = getPeerAddr(sockfd);
  if (localaddr.sin6_family == AF_INET)
  {
    const struct sockaddr_in* laddr4 = reinterpret_cast<struct sockaddr_in*>(&localaddr);
    const struct sockaddr_in* raddr4 = reinterpret_cast<struct sockaddr_in*>(&peeraddr);
    return laddr4->sin_port == raddr4->sin_port
        && laddr4->sin_addr.s_addr == raddr4->sin_addr.s_addr;
  }
  else if (localaddr.sin6_family == AF_INET6)
  {
    return localaddr.sin6_port == peeraddr.sin6_port
        && memcmp(&localaddr.sin6_addr, &peeraddr.sin6_addr, sizeof localaddr.sin6_addr) == 0;
  }
  else
  {
    return false;
  }
}


} // namespace zexuan::net::sockets
