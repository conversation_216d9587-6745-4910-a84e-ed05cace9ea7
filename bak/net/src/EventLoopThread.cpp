#include "zexuan/net/EventLoopThread.hpp"
#include "zexuan/net/EventLoop.hpp"
#include "zexuan/logger.hpp"

namespace zexuan::net {

// Static logger for EventLoopThread
static auto event_loop_thread_logger_ = zexuan::Logger::getFileLogger("net/EventLoopThread");

EventLoopThread::EventLoopThread(const ThreadInitCallback& cb,
                                 const std::string& name)
  : callback_(cb)
{
  event_loop_thread_logger_->info("EventLoopThread [{}] created", name.empty() ? "unnamed" : name);
}

EventLoopThread::~EventLoopThread()
{
  exiting_.store(true);
  if (loop_ != nullptr) {
    // Request loop to quit and wait for thread to finish
    loop_->quit();
    if (thread_.joinable()) {
      thread_.join();
    }
  }
  event_loop_thread_logger_->info("EventLoopThread destroyed");
}

EventLoop* EventLoopThread::startLoop()
{
  // Start the thread
  thread_ = std::thread([this]() { threadFunc(); });

  EventLoop* loop = nullptr;
  {
    std::unique_lock<std::mutex> lock(mutex_);
    // Wait for the loop to be created in the thread
    cond_.wait(lock, [this]() { return loop_ != nullptr; });
    loop = loop_;
  }

  event_loop_thread_logger_->info("EventLoop started in thread");
  return loop;
}

void EventLoopThread::threadFunc()
{
  EventLoop loop;

  if (callback_) {
    callback_(&loop);
  }

  {
    std::lock_guard<std::mutex> lock(mutex_);
    loop_ = &loop;
    cond_.notify_one();
  }

  // Run the event loop
  loop.loop();

  // Clean up
  std::lock_guard<std::mutex> lock(mutex_);
  loop_ = nullptr;
}

} // namespace zexuan::net

