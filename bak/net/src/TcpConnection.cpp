// excerpts from http://code.google.com/p/zexuan/
//
// Use of this source code is governed by a BSD-style license
// that can be found in the License file.
//
// Author: <PERSON><PERSON> (chenshuo at chenshuo dot com)

#include "zexuan/net/TcpConnection.hpp"
#include "zexuan/net/Socket.hpp"
#include "zexuan/net/Channel.hpp"
#include "zexuan/net/EventLoop.hpp"
#include "zexuan/net/SocketsOps.hpp"
#include "zexuan/logger.hpp"

#include <errno.h>
#include <stdio.h>
#include <cassert>

namespace zexuan::net {

// Static logger for TcpConnection
static auto tcpconnection_logger_ = zexuan::Logger::getFileLogger("net/TcpConnection");

namespace {

const char* stateToString(TcpConnection::StateE state) {
  switch (state) {
    case TcpConnection::StateE::kConnecting:    return "kConnecting";
    case TcpConnection::StateE::kConnected:     return "kConnected";
    case TcpConnection::StateE::kDisconnecting: return "kDisconnecting";
    case TcpConnection::StateE::kDisconnected:  return "kDisconnected";
    default:                                    return "unknown state";
  }
}

}

TcpConnection::TcpConnection(EventLoop* loop,
                           std::string name,
                           int sockfd,
                           const InetAddress& localAddr,
                           const InetAddress& peerAddr)
  : loop_(loop),
    name_(std::move(name)),
    state_(StateE::kConnecting),
    socket_(new Socket(sockfd)),
    channel_(new Channel(loop, sockfd)),
    localAddr_(localAddr),
    peerAddr_(peerAddr)
{
  tcpconnection_logger_->debug("TcpConnection::ctor[{}] at {} fd={}", name_, reinterpret_cast<uintptr_t>(this), sockfd);
  channel_->setReadCallback(
      [this](auto receiveTime) { handleRead(receiveTime); });
  channel_->setWriteCallback(
      [this]() { handleWrite(); });
  channel_->setCloseCallback(
      [this]() { handleClose(); });
  channel_->setErrorCallback(
      [this]() { handleError(); });
}

TcpConnection::~TcpConnection()
{
  tcpconnection_logger_->debug("TcpConnection::dtor[{}] at {} fd={}", name_, reinterpret_cast<uintptr_t>(this), channel_->fd());
}

void TcpConnection::send(const std::string& message)
{
  if (state_.load(std::memory_order_acquire) == StateE::kConnected) {
    if (loop_->isInLoopThread()) {
      sendInLoop(message);
    } else {
      loop_->runInLoop(
          [this, message]() { sendInLoop(message); });
    }
  }
}

void TcpConnection::sendInLoop(std::string_view message)
{
  loop_->assertInLoopThread();
  ssize_t nwrote = 0;
  // if no thing in output queue, try writing directly
  if (!channel_->isWriting() && outputBuffer_.readableBytes() == 0) {
    nwrote = ::write(channel_->fd(), message.data(), message.size());
    if (nwrote >= 0) {
      if (static_cast<size_t>(nwrote) < message.size()) {
        tcpconnection_logger_->debug("I am going to write more data");
      } else if (writeCompleteCallback_) {
        loop_->queueInLoop([this]() { writeCompleteCallback_(shared_from_this()); });
      }
    } else {
      nwrote = 0;
      if (errno != EWOULDBLOCK) {
        tcpconnection_logger_->error("TcpConnection::sendInLoop");
      }
    }
  }

  assert(nwrote >= 0);
  if (static_cast<size_t>(nwrote) < message.size()) {
    outputBuffer_.append(message.data()+nwrote, message.size()-nwrote);
    if (!channel_->isWriting()) {
      channel_->enableWriting();
    }
  }
}

void TcpConnection::shutdown()
{
  if (state_.load(std::memory_order_acquire) == StateE::kConnected) {
    setState(StateE::kDisconnecting);
    loop_->runInLoop([this]() { shutdownInLoop(); });
  }
}

void TcpConnection::shutdownInLoop()
{
  loop_->assertInLoopThread();
  if (!channel_->isWriting()) {
    socket_->shutdownWrite();
  }
}

void TcpConnection::setTcpNoDelay(bool on)
{
  socket_->setTcpNoDelay(on);
}

void TcpConnection::connectEstablished()
{
  loop_->assertInLoopThread();
  assert(state_.load(std::memory_order_acquire) == StateE::kConnecting);
  setState(StateE::kConnected);
  channel_->enableReading();

  connectionCallback_(shared_from_this());
}

void TcpConnection::connectDestroyed()
{
  loop_->assertInLoopThread();
  auto current_state = state_.load(std::memory_order_acquire);
  assert(current_state == StateE::kConnected || current_state == StateE::kDisconnecting);
  setState(StateE::kDisconnected);
  channel_->disableAll();

  connectionCallback_(shared_from_this());

  loop_->removeChannel(channel_.get());
}

void TcpConnection::handleRead(std::chrono::system_clock::time_point receiveTime)
{
  int savedErrno = 0;
  ssize_t n = inputBuffer_.readFd(channel_->fd(), &savedErrno);
  if (n > 0) {
    messageCallback_(shared_from_this(), &inputBuffer_, receiveTime);
  } else if (n == 0) {
    handleClose();
  } else {
    errno = savedErrno;
    tcpconnection_logger_->error("TcpConnection::handleRead");
    handleError();
  }
}

void TcpConnection::handleWrite()
{
  loop_->assertInLoopThread();
  if (channel_->isWriting()) {
    ssize_t n = ::write(channel_->fd(),
                       outputBuffer_.peek(),
                       outputBuffer_.readableBytes());
    if (n > 0) {
      outputBuffer_.retrieve(n);
      if (outputBuffer_.readableBytes() == 0) {
        channel_->disableWriting();
        if (writeCompleteCallback_) {
          loop_->queueInLoop([this]() { writeCompleteCallback_(shared_from_this()); });
        }
        if (state_.load(std::memory_order_acquire) == StateE::kDisconnecting) {
          shutdownInLoop();
        }
      } else {
        tcpconnection_logger_->debug("I am going to write more data");
      }
    } else {
      tcpconnection_logger_->error("TcpConnection::handleWrite");
    }
  } else {
    tcpconnection_logger_->debug("Connection is down, no more writing");
  }
}

void TcpConnection::handleClose()
{
  loop_->assertInLoopThread();
  auto current_state = state_.load(std::memory_order_acquire);
  tcpconnection_logger_->debug("TcpConnection::handleClose state = {}", stateToString(current_state));
  assert(current_state == StateE::kConnected || current_state == StateE::kDisconnecting);
  // we don't close fd, leave it to dtor, so we can find leaks easily.
  channel_->disableAll();
  // must be the last line
  closeCallback_(shared_from_this());
}

void TcpConnection::handleError()
{
  int err = sockets::getSocketError(channel_->fd());
  tcpconnection_logger_->error("TcpConnection::handleError [{}] - SO_ERROR = {} {}", name_, err, strerror(err));
}

} // namespace zexuan::net
