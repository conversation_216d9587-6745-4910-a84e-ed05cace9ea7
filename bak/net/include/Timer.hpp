#pragma once

#include "zexuan/base/noncopyable.hpp"
#include "Callbacks.hpp"

#include <atomic>
#include <chrono>
#include <memory>

namespace zexuan::net
{

class Timer : public zexuan::base::noncopyable
{
 public:
  // Modern constructor with perfect forwarding
  template<typename Callback>
  Timer(Callback&& cb, TimePoint when, Duration interval)
    : callback_(std::forward<Callback>(cb)),
      expiration_(when),
      interval_(interval),
      repeat_(interval.count() > 0),
      sequence_(s_numCreated_.fetch_add(1, std::memory_order_relaxed))
  {
  }

  void run() const {
    callback_();
  }

  [[nodiscard]] TimePoint expiration() const noexcept { return expiration_; }
  [[nodiscard]] bool repeat() const noexcept { return repeat_; }
  [[nodiscard]] int64_t sequence() const noexcept { return sequence_; }

  void restart(TimePoint now) noexcept {
    if (repeat_) {
      expiration_ = now + interval_;
    } else {
      expiration_ = TimePoint{};
    }
  }

 private:
  const TimerCallback callback_;
  TimePoint expiration_;
  const Duration interval_;
  const bool repeat_;
  const int64_t sequence_;

  static std::atomic<int64_t> s_numCreated_;
};

using TimerPtr = std::shared_ptr<Timer>;
using TimerWeakPtr = std::weak_ptr<Timer>;

} // namespace zexuan::net