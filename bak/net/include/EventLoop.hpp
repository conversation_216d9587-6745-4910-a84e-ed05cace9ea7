#pragma once

#include "zexuan/base/noncopyable.hpp"
#include "Callbacks.hpp"
#include "Timer.hpp"
#include "zexuan/logger.hpp"

#include <memory>
#include <vector>
#include <functional>
#include <mutex>
#include <chrono>
#include <thread>
#include <atomic>
#include <spdlog/spdlog.h>

namespace zexuan::net
{

// Forward declarations
class Channel;
class EPoller;
class Timer;
class TimerQueue;

// Simple callback type for EventLoop operations

/// Modern event loop with C++20 features
/// Thread-safe for cross-thread operations
class EventLoop : public zexuan::base::noncopyable
{
 public:
  using TimePoint = std::chrono::system_clock::time_point;
  using Duration = std::chrono::milliseconds;
  using Functor = std::function<void()>;
  using TimerHandle = std::weak_ptr<Timer>;

  EventLoop();
  ~EventLoop();

  /// Start the event loop (blocks until quit() is called)
  void loop();

  /// Request the event loop to quit
  /// Thread-safe: can be called from any thread
  void quit();

  [[nodiscard]] TimePoint pollReturnTime() const noexcept { return pollReturnTime_; }

  /// Execute callback in the event loop thread
  /// If called from the event loop thread, executes immediately
  /// Otherwise, queues for execution in the next iteration
  void runInLoop(const Functor& cb);

  /// Queue callback for execution in the next event loop iteration
  /// Always queues, even if called from the event loop thread
  void queueInLoop(const Functor& cb);

  /// Timer management functions
  [[nodiscard]] TimerHandle runAt(const TimePoint& time, const TimerCallback& cb);
  [[nodiscard]] TimerHandle runAfter(Duration delay, const TimerCallback& cb);
  [[nodiscard]] TimerHandle runEvery(Duration interval, const TimerCallback& cb);

  void cancel(const TimerHandle& timer);

  /// Channel management
  void wakeup();
  void updateChannel(Channel* channel);
  void removeChannel(Channel* channel);

  /// Thread safety checks
  void assertInLoopThread() {
    if (!isInLoopThread()) {
      abortNotInLoopThread();
    }
  }

  [[nodiscard]] bool isInLoopThread() const noexcept {
    return threadId_ == std::this_thread::get_id();
  }

 private:
  void abortNotInLoopThread();
  void handleRead();  // Handle wakeup events
  void doPendingFunctors();

  using ChannelList = std::vector<Channel*>;

  // Atomic flags for thread-safe state management
  std::atomic<bool> looping_{false};
  std::atomic<bool> quit_{false};
  std::atomic<bool> callingPendingFunctors_{false};

  const std::thread::id threadId_;
  TimePoint pollReturnTime_;

  // RAII managed resources
  std::unique_ptr<EPoller> poller_;
  std::unique_ptr<TimerQueue> timerQueue_;

  int wakeupFd_;
  std::unique_ptr<Channel> wakeupChannel_;

  ChannelList activeChannels_;

  // Thread-safe pending functors queue
  mutable std::mutex mutex_;
  std::vector<Functor> pendingFunctors_; // Guarded by mutex_

  // Logger for this class
  std::shared_ptr<spdlog::logger> eventloop_logger_;
};

} // namespace zexuan::net
