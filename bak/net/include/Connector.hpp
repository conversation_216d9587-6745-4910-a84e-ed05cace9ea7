#pragma once

#include "zexuan/base/noncopyable.hpp"
#include "zexuan/net/InetAddress.hpp"

#include <functional>
#include <memory>
#include <atomic>

namespace zexuan::net
{

class Channel;
class EventLoop;

/// TCP connector for establishing outbound connections
/// Handles connection establishment with retry logic
class Connector : public zexuan::base::noncopyable,
                  public std::enable_shared_from_this<Connector>
{
 public:
  using NewConnectionCallback = std::function<void(int sockfd)>;

  Connector(EventLoop* loop, const InetAddress& serverAddr);
  ~Connector();

  // Callback setter
  void setNewConnectionCallback(const NewConnectionCallback& cb) {
    newConnectionCallback_ = cb;
  }

  // Connection control - thread safe
  void start();   // Can be called in any thread
  void restart(); // Must be called in loop thread
  void stop();    // Can be called in any thread

  // Accessor
  [[nodiscard]] const InetAddress& serverAddress() const noexcept {
    return serverAddr_;
  }

 private:
  enum class States {
    kDisconnected,
    kConnecting,
    kConnected
  };

  static constexpr int kMaxRetryDelayMs = 30 * 1000;
  static constexpr int kInitRetryDelayMs = 500;

  // Internal state management
  void setState(States s) noexcept { state_.store(s); }
  void startInLoop();
  void stopInLoop();
  void connect();
  void connecting(int sockfd);
  void handleWrite();
  void handleError();
  void retry(int sockfd);
  int removeAndResetChannel();
  void resetChannel();

  EventLoop* loop_;
  InetAddress serverAddr_;
  std::atomic<bool> connect_{false};
  std::atomic<States> state_{States::kDisconnected};
  std::unique_ptr<Channel> channel_;
  NewConnectionCallback newConnectionCallback_;
  int retryDelayMs_{kInitRetryDelayMs};
};

} // namespace zexuan::net
