#pragma once

#include "zexuan/base/noncopyable.hpp"
#include "Buffer.hpp"
#include "Callbacks.hpp"
#include "InetAddress.hpp"

#include <memory>
#include <string>
#include <any>
#include <atomic>

namespace zexuan::net
{

// Forward declarations
class Channel;
class EventLoop;
class Socket;

/// Modern TCP connection class for both client and server usage
/// Uses RAII for resource management and atomic state for thread safety
class TcpConnection : public zexuan::base::noncopyable,
                     public std::enable_shared_from_this<TcpConnection>
{
 public:
  enum class StateE {
    kConnecting,
    kConnected,
    kDisconnecting,
    kDisconnected
  };

  /// Constructs a TcpConnection with a connected sockfd
  /// User should not create this object directly
  TcpConnection(EventLoop* loop,
                std::string name,
                int sockfd,
                const InetAddress& localAddr,
                const InetAddress& peerAddr);
  ~TcpConnection();

  [[nodiscard]] EventLoop* getLoop() const noexcept { return loop_; }
  [[nodiscard]] const std::string& name() const noexcept { return name_; }
  [[nodiscard]] const InetAddress& localAddress() const noexcept { return localAddr_; }
  [[nodiscard]] const InetAddress& peerAddress() const noexcept { return peerAddr_; }
  [[nodiscard]] bool connected() const noexcept {
    return state_.load(std::memory_order_acquire) == StateE::kConnected;
  }

  /// Send data (thread-safe)
  void send(const std::string& message);
  void send(std::string_view message);
  void send(Buffer&& buffer);

  /// Shutdown write side of connection (thread-safe)
  void shutdown();

  /// Set TCP_NODELAY option
  void setTcpNoDelay(bool on);

  /// Callback setters - std::function versions (exact match, highest priority)
  void setConnectionCallback(const ConnectionCallback& cb) {
    connectionCallback_ = cb;
  }
  void setConnectionCallback(ConnectionCallback&& cb) {
    connectionCallback_ = std::move(cb);
  }

  void setMessageCallback(const MessageCallback& cb) {
    messageCallback_ = cb;
  }
  void setMessageCallback(MessageCallback&& cb) {
    messageCallback_ = std::move(cb);
  }

  void setWriteCompleteCallback(const WriteCompleteCallback& cb) {
    writeCompleteCallback_ = cb;
  }
  void setWriteCompleteCallback(WriteCompleteCallback&& cb) {
    writeCompleteCallback_ = std::move(cb);
  }



  /// Internal use only - set close callback
  void setCloseCallback(const CloseCallback& cb) {
    closeCallback_ = cb;
  }

  /// Connection lifecycle management (called by TcpServer)
  void connectEstablished();   // should be called only once
  void connectDestroyed();     // should be called only once

 private:
  void setState(StateE s) noexcept {
    state_.store(s, std::memory_order_release);
  }

  void handleRead(TimePoint receiveTime);
  void handleWrite();
  void handleClose();
  void handleError();
  void sendInLoop(std::string_view message);
  void shutdownInLoop();

  EventLoop* loop_;
  const std::string name_;
  std::atomic<StateE> state_;  // Thread-safe state management

  // RAII managed resources - not exposed to client
  std::unique_ptr<Socket> socket_;
  std::unique_ptr<Channel> channel_;
  const InetAddress localAddr_;
  const InetAddress peerAddr_;

  // Callbacks
  ConnectionCallback connectionCallback_;
  MessageCallback messageCallback_;
  WriteCompleteCallback writeCompleteCallback_;
  CloseCallback closeCallback_;

  // Buffers
  Buffer inputBuffer_;
  Buffer outputBuffer_;
};

// Type alias for shared ownership
using TcpConnectionPtr = std::shared_ptr<TcpConnection>;

} // namespace zexuan::net
