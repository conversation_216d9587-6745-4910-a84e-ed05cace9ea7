#pragma once

#include "zexuan/base/noncopyable.hpp"
#include "zexuan/net/Callbacks.hpp"
#include "zexuan/net/TcpConnection.hpp"
#include "zexuan/net/InetAddress.hpp"

#include <map>
#include <memory>
#include <string>
#include <atomic>
#include <functional>

namespace zexuan::net
{

class Acceptor;
class EventLoop;
class EventLoopThreadPool;

/// Modern TCP server with thread pool support
/// Supports single-threaded and multi-threaded models
/// Thread-safe for configuration, not thread-safe for callbacks
class TcpServer : public zexuan::base::noncopyable
{
 public:
  using ThreadInitCallback = std::function<void(EventLoop*)>;

  enum class Option {
    NoReusePort,
    ReusePort,
  };

  TcpServer(EventLoop* loop,
            const InetAddress& listenAddr,
            const std::string& nameArg,
            Option option = Option::NoReusePort);
  ~TcpServer();  // force out-line dtor, for std::unique_ptr members

  // Accessors
  [[nodiscard]] const std::string& ipPort() const noexcept { return ipPort_; }
  [[nodiscard]] const std::string& name() const noexcept { return name_; }
  [[nodiscard]] EventLoop* getLoop() const noexcept { return loop_; }

  /// Set the number of threads for handling input
  /// Always accepts new connection in loop's thread
  /// Must be called before start()
  /// @param numThreads
  /// - 0 means all I/O in loop's thread, no thread will created (default)
  /// - 1 means all I/O in another thread
  /// - N means a thread pool with N threads, new connections assigned round-robin
  void setThreadNum(int numThreads);

  void setThreadInitCallback(const ThreadInitCallback& cb) {
    threadInitCallback_ = cb;
  }

  void setThreadInitCallback(ThreadInitCallback&& cb) {
    threadInitCallback_ = std::move(cb);
  }

  /// Valid after calling start()
  [[nodiscard]] std::shared_ptr<EventLoopThreadPool> threadPool() {
    return threadPool_;
  }

  /// Starts the server if it's not listening
  /// Thread safe - can be called multiple times harmlessly
  void start();

  // Callback setters - not thread safe, call before start()
  void setConnectionCallback(const ConnectionCallback& cb) {
    connectionCallback_ = cb;
  }

  void setConnectionCallback(ConnectionCallback&& cb) {
    connectionCallback_ = std::move(cb);
  }

  void setMessageCallback(const MessageCallback& cb) {
    messageCallback_ = cb;
  }

  void setMessageCallback(MessageCallback&& cb) {
    messageCallback_ = std::move(cb);
  }

  void setWriteCompleteCallback(const WriteCompleteCallback& cb) {
    writeCompleteCallback_ = cb;
  }

  void setWriteCompleteCallback(WriteCompleteCallback&& cb) {
    writeCompleteCallback_ = std::move(cb);
  }

 private:
  // Internal methods - not thread safe, but called in loop thread
  void newConnection(int sockfd, const InetAddress& peerAddr);
  void removeConnectionInLoop(const TcpConnectionPtr& conn);

  // Thread safe method
  void removeConnection(const TcpConnectionPtr& conn);

  using ConnectionMap = std::map<std::string, TcpConnectionPtr>;

  // Core components
  EventLoop* loop_;  // the acceptor loop
  const std::string ipPort_;
  const std::string name_;
  std::unique_ptr<Acceptor> acceptor_;
  std::shared_ptr<EventLoopThreadPool> threadPool_;

  // Callbacks
  ConnectionCallback connectionCallback_;
  MessageCallback messageCallback_;
  WriteCompleteCallback writeCompleteCallback_;
  ThreadInitCallback threadInitCallback_;

  // Thread-safe state
  std::atomic<bool> started_{false};

  // Loop thread only
  int nextConnId_{1};
  ConnectionMap connections_;
};

} // namespace zexuan::net
