#pragma once

#include "zexuan/base/noncopyable.hpp"
#include "zexuan/logger.hpp"

#include <functional>
#include <chrono>

#include <spdlog/spdlog.h>

namespace zexuan::net
{

class EventLoop;

/// A selectable I/O channel with modern C++ features
/// This class doesn't own the file descriptor.
/// The file descriptor could be a socket, eventfd, timerfd, or signalfd
/// Uses RAII for resource management and modern callback handling
class Channel : public zexuan::base::noncopyable
{
 public:
  using TimePoint = std::chrono::system_clock::time_point;
  using EventCallback = std::function<void()>;
  using ReadEventCallback = std::function<void(TimePoint)>;

  Channel(EventLoop* loop, int fd);
  ~Channel();

  void handleEvent(TimePoint receiveTime);

  // Callback setters - simple and direct
  void setReadCallback(const ReadEventCallback& cb) {
    readCallback_ = cb;
  }
  void setReadCallback(ReadEventCallback&& cb) {
    readCallback_ = std::move(cb);
  }

  void setWriteCallback(const EventCallback& cb) {
    writeCallback_ = cb;
  }
  void setWriteCallback(EventCallback&& cb) {
    writeCallback_ = std::move(cb);
  }

  void setErrorCallback(const EventCallback& cb) {
    errorCallback_ = cb;
  }
  void setErrorCallback(EventCallback&& cb) {
    errorCallback_ = std::move(cb);
  }

  void setCloseCallback(const EventCallback& cb) {
    closeCallback_ = cb;
  }
  void setCloseCallback(EventCallback&& cb) {
    closeCallback_ = std::move(cb);
  }

  [[nodiscard]] int fd() const noexcept { return fd_; }
  [[nodiscard]] int events() const noexcept { return events_; }
  void set_revents(int revt) noexcept { revents_ = revt; }
  [[nodiscard]] bool isNoneEvent() const noexcept { return events_ == kNoneEvent; }

  void enableReading() { events_ |= kReadEvent; update(); }
  void enableWriting() { events_ |= kWriteEvent; update(); }
  void disableReading() { events_ &= ~kReadEvent; update(); }
  void disableWriting() { events_ &= ~kWriteEvent; update(); }
  void disableAll() { events_ = kNoneEvent; update(); }
  [[nodiscard]] bool isReading() const noexcept { return events_ & kReadEvent; }
  [[nodiscard]] bool isWriting() const noexcept { return events_ & kWriteEvent; }

  // For Poller interface
  [[nodiscard]] int index() const noexcept { return index_; }
  void set_index(int idx) noexcept { index_ = idx; }

  [[nodiscard]] EventLoop* ownerLoop() const noexcept { return loop_; }

  // Channel management
  void remove();

 public:
  // Event constants - made public for testing
  static constexpr int kNoneEvent = 0;
  static constexpr int kReadEvent = 0x001 | 0x002;  // POLLIN | POLLPRI
  static constexpr int kWriteEvent = 0x004;          // POLLOUT

 private:
  void update();

  EventLoop* loop_;
  const int fd_;
  int events_;
  int revents_;
  int index_; // used by Poller

  bool eventHandling_;

  ReadEventCallback readCallback_;
  EventCallback writeCallback_;
  EventCallback errorCallback_;
  EventCallback closeCallback_;

  // Logger for this class
  std::shared_ptr<spdlog::logger> channel_logger_;
};

} // namespace zexuan::net
