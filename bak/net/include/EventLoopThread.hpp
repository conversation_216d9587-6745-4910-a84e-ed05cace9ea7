#pragma once

#include "zexuan/base/noncopyable.hpp"

#include <functional>
#include <string>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>

namespace zexuan::net
{

class EventLoop;

/// EventLoop thread wrapper
/// Creates and manages an EventLoop in a separate thread
class EventLoopThread : public zexuan::base::noncopyable
{
 public:
  using ThreadInitCallback = std::function<void(EventLoop*)>;

  EventLoopThread(const ThreadInitCallback& cb = ThreadInitCallback(),
                  const std::string& name = std::string());
  ~EventLoopThread();

  /// Start the event loop in a new thread
  /// Returns pointer to the EventLoop (thread-safe)
  [[nodiscard]] EventLoop* startLoop();

 private:
  void threadFunc();

  EventLoop* loop_{nullptr};
  std::atomic<bool> exiting_{false};
  std::thread thread_;
  std::mutex mutex_;
  std::condition_variable cond_;
  ThreadInitCallback callback_;
};

} // namespace zexuan::net

