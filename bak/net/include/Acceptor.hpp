#pragma once

#include "zexuan/base/noncopyable.hpp"
#include "zexuan/net/Channel.hpp"
#include "zexuan/net/Socket.hpp"
#include "zexuan/net/InetAddress.hpp"

#include <functional>

namespace zexuan::net
{

class EventLoop;

/// Acceptor for incoming TCP connections
/// Internal class - handles socket accept operations
class Acceptor : public zexuan::base::noncopyable
{
 public:
  using NewConnectionCallback = std::function<void(int sockfd, const InetAddress&)>;

  Acceptor(EventLoop* loop, const InetAddress& listenAddr, bool reuseport);
  ~Acceptor();

  // Callback setters
  void setNewConnectionCallback(const NewConnectionCallback& cb) {
    newConnectionCallback_ = cb;
  }

  void setNewConnectionCallback(NewConnectionCallback&& cb) {
    newConnectionCallback_ = std::move(cb);
  }

  // Control methods
  void listen();
  [[nodiscard]] bool listening() const noexcept { return listening_; }

 private:
  void handleRead();

  EventLoop* loop_;
  Socket acceptSocket_;
  Channel acceptChannel_;
  NewConnectionCallback newConnectionCallback_;
  bool listening_{false};
  int idleFd_;  // Used for handling EMFILE error
};

} // namespace zexuan::net
