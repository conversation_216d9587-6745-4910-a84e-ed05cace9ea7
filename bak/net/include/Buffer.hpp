#pragma once

#include "zexuan/base/copyable.hpp"

#include <algorithm>
#include <string>
#include <string_view>
#include <vector>
#include <ranges>
#include <cassert>


namespace zexuan::net
{

/// A modern buffer class with std::span support
/// Modeled after org.jboss.netty.buffer.ChannelBuffer but using modern C++
///
/// @code
/// +-------------------+------------------+------------------+
/// | prependable bytes |  readable bytes  |  writable bytes  |
/// |                   |     (CONTENT)    |                  |
/// +-------------------+------------------+------------------+
/// |                   |                  |                  |
/// 0      <=      readerIndex   <=   writerIndex    <=     size
/// @endcode
class Buffer : public zexuan::base::copyable
{
 public:
  static constexpr size_t kCheapPrepend = 8;
  static constexpr size_t kInitialSize = 1024;

  Buffer() noexcept
    : buffer_(kCheapPrepend + kInitialSize),
      readerIndex_(kCheapPrepend),
      writerIndex_(kCheapPrepend)
  {
    assert(readableBytes() == 0);
    assert(writableBytes() == kInitialSize);
    assert(prependableBytes() == kCheapPrepend);
  }

  // default copy-ctor, dtor and assignment are fine
  // Rule of Zero - let compiler generate them

  void swap(Buffer& rhs) noexcept {
    buffer_.swap(rhs.buffer_);
    std::swap(readerIndex_, rhs.readerIndex_);
    std::swap(writerIndex_, rhs.writerIndex_);
  }

  [[nodiscard]] size_t readableBytes() const noexcept {
    return writerIndex_ - readerIndex_;
  }

  [[nodiscard]] size_t writableBytes() const noexcept {
    return buffer_.size() - writerIndex_;
  }

  [[nodiscard]] size_t prependableBytes() const noexcept {
    return readerIndex_;
  }

  // Modern string_view interface - simple and widely supported
  [[nodiscard]] std::string_view readableView() const noexcept {
    return {data() + readerIndex_, readableBytes()};
  }

  // Legacy interface for backward compatibility
  [[nodiscard]] const char* peek() const noexcept {
    return data() + readerIndex_;
  }

  // retrieve returns void, to prevent
  // string str(retrieve(readableBytes()), readableBytes());
  // the evaluation of two functions are unspecified
  void retrieve(size_t len) {
    assert(len <= readableBytes());
    readerIndex_ += len;
  }

  void retrieveUntil(const char* end) {
    assert(peek() <= end);
    assert(end <= beginWrite());
    retrieve(static_cast<size_t>(end - peek()));
  }

  void retrieveAll() noexcept {
    readerIndex_ = kCheapPrepend;
    writerIndex_ = kCheapPrepend;
  }

  [[nodiscard]] std::string retrieveAsString() {
    auto view = readableView();
    std::string result{view};
    retrieveAll();
    return result;
  }

  // Modern string_view based retrieval
  [[nodiscard]] std::string_view retrieveAsStringView(size_t len) {
    assert(len <= readableBytes());
    auto view = std::string_view{peek(), len};
    retrieve(len);
    return view;
  }

  // Append methods - simple and direct
  void append(std::string_view str) {
    append(str.data(), str.size());
  }

  void append(const char* data, size_t len) {
    ensureWritableBytes(len);
    std::ranges::copy_n(data, len, beginWrite());
    hasWritten(len);
  }

  void append(const void* data, size_t len) {
    append(static_cast<const char*>(data), len);
  }



  void ensureWritableBytes(size_t len) {
    if (writableBytes() < len) {
      makeSpace(len);
    }
    assert(writableBytes() >= len);
  }

  [[nodiscard]] char* beginWrite() noexcept {
    return data() + writerIndex_;
  }

  [[nodiscard]] const char* beginWrite() const noexcept {
    return data() + writerIndex_;
  }

  void hasWritten(size_t len) noexcept {
    writerIndex_ += len;
  }

  void prepend(const void* data, size_t len) {
    assert(len <= prependableBytes());
    readerIndex_ -= len;
    const char* src = static_cast<const char*>(data);
    std::ranges::copy_n(src, len, this->data() + readerIndex_);
  }



  void shrink(size_t reserve) {
    std::vector<char> buf(kCheapPrepend + readableBytes() + reserve);
    auto readable_size = readableBytes();
    std::ranges::copy_n(peek(), readable_size, buf.begin() + kCheapPrepend);
    buf.swap(buffer_);
    readerIndex_ = kCheapPrepend;
    writerIndex_ = readerIndex_ + readable_size;
  }

  /// Read data directly into buffer using readv(2)
  /// @return result of read(2), errno is saved in savedErrno
  ssize_t readFd(int fd, int* savedErrno);

 private:
  // Modern data access using vector's data() method
  [[nodiscard]] char* data() noexcept {
    return buffer_.data();
  }

  [[nodiscard]] const char* data() const noexcept {
    return buffer_.data();
  }

  void makeSpace(size_t len) {
    if (writableBytes() + prependableBytes() < len + kCheapPrepend) {
      // Need to grow buffer
      buffer_.resize(writerIndex_ + len);
    } else {
      // Move readable data to the front, make space inside buffer
      assert(kCheapPrepend < readerIndex_);
      auto readable_size = readableBytes();
      std::ranges::copy_n(peek(), readable_size, data() + kCheapPrepend);
      readerIndex_ = kCheapPrepend;
      writerIndex_ = readerIndex_ + readable_size;
      assert(readable_size == readableBytes());
    }
  }

 private:
  std::vector<char> buffer_;
  size_t readerIndex_;
  size_t writerIndex_;
};

} // namespace zexuan::net
