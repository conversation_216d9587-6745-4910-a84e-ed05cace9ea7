#pragma once

#include <memory>
#include <functional>
#include <chrono>

namespace zexuan::net
{

// Forward declarations
class Buffer;
class TcpConnection;
class Timer;

// Type aliases for shared pointers
using TcpConnectionPtr = std::shared_ptr<TcpConnection>;
using TcpConnectionWeakPtr = std::weak_ptr<TcpConnection>;

// Time point type alias for consistency
using TimePoint = std::chrono::system_clock::time_point;
using Duration = std::chrono::milliseconds;

// Callback type definitions - simple and direct
using TimerCallback = std::function<void()>;
using ConnectionCallback = std::function<void(const TcpConnectionPtr&)>;
using MessageCallback = std::function<void(const TcpConnectionPtr&, Buffer*, TimePoint)>;
using WriteCompleteCallback = std::function<void(const TcpConnectionPtr&)>;
using CloseCallback = std::function<void(const TcpConnectionPtr&)>;
using HighWaterMarkCallback =  std::function<void (const TcpConnectionPtr&, size_t)>;

// Default callback implementations
void defaultConnectionCallback(const TcpConnectionPtr& conn);
void defaultMessageCallback(const TcpConnectionPtr& conn, Buffer* buffer, TimePoint receiveTime);

} // namespace zexuan::net
