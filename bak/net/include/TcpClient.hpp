#pragma once

#include "zexuan/base/noncopyable.hpp"
#include "zexuan/net/TcpConnection.hpp"
#include "zexuan/net/Callbacks.hpp"
#include "zexuan/net/InetAddress.hpp"

#include <memory>
#include <string>
#include <mutex>
#include <atomic>

namespace zexuan::net
{

class Connector;
class EventLoop;

/// TCP client for connecting to remote servers
/// Manages connection lifecycle and provides callback interfaces
class TcpClient : public zexuan::base::noncopyable
{
 public:
  /// Constructor
  /// @param loop Event loop to run in
  /// @param serverAddr Server address to connect to
  /// @param nameArg Client name for identification
  TcpClient(EventLoop* loop,
            const InetAddress& serverAddr,
            const std::string& nameArg);

  /// Destructor - force out-line for std::unique_ptr members
  ~TcpClient();

  // Connection management
  void connect();
  void disconnect();
  void stop();

  // Accessors
  [[nodiscard]] TcpConnectionPtr connection() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return connection_;
  }

  [[nodiscard]] EventLoop* getLoop() const noexcept { return loop_; }
  [[nodiscard]] bool retry() const noexcept { return retry_.load(); }
  void enableRetry() noexcept { retry_.store(true); }

  [[nodiscard]] const std::string& name() const noexcept { return name_; }

  // Callback setters - not thread safe, call before connect()
  void setConnectionCallback(ConnectionCallback cb) {
    connectionCallback_ = std::move(cb);
  }

  void setMessageCallback(MessageCallback cb) {
    messageCallback_ = std::move(cb);
  }

  void setWriteCompleteCallback(WriteCompleteCallback cb) {
    writeCompleteCallback_ = std::move(cb);
  }

 private:
  // Internal connection management - called in loop thread
  void newConnection(int sockfd);
  void removeConnection(const TcpConnectionPtr& conn);

  EventLoop* loop_;
  std::unique_ptr<Connector> connector_;
  const std::string name_;

  // Callbacks
  ConnectionCallback connectionCallback_;
  MessageCallback messageCallback_;
  WriteCompleteCallback writeCompleteCallback_;

  // Thread-safe state
  std::atomic<bool> retry_{false};
  std::atomic<bool> connect_{false};

  // Loop thread only
  int nextConnId_{1};

  // Thread-safe connection access
  mutable std::mutex mutex_;
  TcpConnectionPtr connection_;
};

} // namespace zexuan::net
