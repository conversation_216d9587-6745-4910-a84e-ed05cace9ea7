#pragma once

#include "Channel.hpp"
#include "zexuan/base/noncopyable.hpp"
#include "Timer.hpp"
#include "zexuan/logger.hpp"

#include <set>
#include <vector>
#include <memory>
#include <chrono>
#include <queue>
#include <functional>
#include <spdlog/spdlog.h>

namespace zexuan::net
{

class EventLoop;

/// A modern timer queue using C++20 features
/// Best effort timer queue - no guarantee that callbacks will be exactly on time
/// Thread-safe for adding/canceling timers from different threads
class TimerQueue : public zexuan::base::noncopyable
{
 public:
  using TimePoint = Timer::TimePoint;
  using Duration = Timer::Duration;

  explicit TimerQueue(EventLoop* loop);
  ~TimerQueue();

  /// Schedules the callback to be run at given time
  /// @param cb Callback function to execute
  /// @param when Absolute time point when to execute
  /// @param interval Repeat interval (0 for one-shot timer)
  /// @return Weak pointer to the timer for cancellation
  /// Thread-safe: can be called from any thread
  [[nodiscard]] TimerWeakPtr addTimer(const TimerCallback& cb,
                                     TimePoint when,
                                     Duration interval);

  /// Cancel a timer
  /// @param timerId Timer to cancel (obtained from addTimer)
  /// Thread-safe: can be called from any thread
  void cancel(const TimerWeakPtr& timerId);

 private:
  // Modern type aliases for better readability
  using Entry = std::pair<TimePoint, TimerPtr>;
  using TimerList = std::set<Entry>;
  using ActiveTimer = std::pair<Timer*, int64_t>;
  using ActiveTimerSet = std::set<ActiveTimer>;

  // Internal methods
  void addTimerInLoop(TimerPtr timer);
  void cancelInLoop(const TimerWeakPtr& timer);

  /// Called when timerfd becomes readable (timer expired)
  void handleRead();

  /// Extract all expired timers
  [[nodiscard]] std::vector<Entry> getExpired(TimePoint now);

  /// Reset timers that need to repeat
  void reset(const std::vector<Entry>& expired, TimePoint now);

  /// Insert timer into the queue
  [[nodiscard]] bool insert(TimerPtr timer);

  // Member variables
  EventLoop* loop_;
  const int timerfd_;
  Channel timerfdChannel_;

  // Timer list sorted by expiration time
  TimerList timers_;

  // For cancellation support
  ActiveTimerSet activeTimers_;
  std::atomic<bool> callingExpiredTimers_{false};
  ActiveTimerSet cancelingTimers_;

  // Logger for this class
  std::shared_ptr<spdlog::logger> timerqueue_logger_;
};

} // namespace zexuan::net
