#pragma once

#include "zexuan/base/noncopyable.hpp"

#include <functional>
#include <memory>
#include <vector>
#include <string>
#include <atomic>

namespace zexuan::net
{

class EventLoop;
class EventLoopThread;

/// Thread pool for EventLoop instances
/// Manages a pool of EventLoop threads for load balancing
class EventLoopThreadPool : public zexuan::base::noncopyable
{
 public:
  using ThreadInitCallback = std::function<void(EventLoop*)>;

  EventLoopThreadPool(EventLoop* baseLoop, const std::string& nameArg);
  ~EventLoopThreadPool();

  // Configuration - must be called before start()
  void setThreadNum(int numThreads) { numThreads_ = numThreads; }

  // Start the thread pool
  void start(const ThreadInitCallback& cb = ThreadInitCallback());

  // Loop selection methods - valid after calling start()
  /// Get next loop using round-robin algorithm
  [[nodiscard]] EventLoop* getNextLoop();

  /// Get loop for specific hash code - same hash always returns same loop
  [[nodiscard]] EventLoop* getLoopForHash(size_t hashCode);

  /// Get all loops in the pool
  [[nodiscard]] std::vector<EventLoop*> getAllLoops();

  // Status queries
  [[nodiscard]] bool started() const noexcept { return started_; }
  [[nodiscard]] const std::string& name() const noexcept { return name_; }

 private:
  EventLoop* baseLoop_;
  std::string name_;
  std::atomic<bool> started_{false};
  int numThreads_{0};
  std::atomic<int> next_{0};  // For round-robin selection
  std::vector<std::unique_ptr<EventLoopThread>> threads_;
  std::vector<EventLoop*> loops_;
};

} // namespace zexuan::net
