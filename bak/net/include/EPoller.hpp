#ifndef NET_EPOLLER_H
#define NET_EPOLLER_H

#include "zexuan/base/noncopyable.hpp"
#include "zexuan/net/EventLoop.hpp"

#include <vector>
#include <map>
#include <chrono>

struct epoll_event;

namespace zexuan::net
{

class Channel;

///
/// IO Multiplexing with epoll(4).
///
class EPoller : public zexuan::base::noncopyable
{
 public:
  using TimePoint = std::chrono::system_clock::time_point;
  using ChannelList = std::vector<Channel*>;

  EPoller(EventLoop* loop);
  ~EPoller();

  /// Polls the I/O events.
  /// Must be called in the loop thread.
  TimePoint poll(int timeoutMs, ChannelList* activeChannels);

  /// Changes the interested I/O events.
  /// Must be called in the loop thread.
  void updateChannel(Channel* channel);
  /// Remove the channel, when it destructs.
  /// Must be called in the loop thread.
  void removeChannel(Channel* channel);

 private:
  static const int kInitEventListSize = 16;

  void fillActiveChannels(int numEvents,
                         ChannelList* activeChannels) const;
  void update(int operation, Channel* channel);

  using EventList = std::vector<struct epoll_event>;
  using ChannelMap = std::map<int, Channel*>;

  EventLoop* ownerLoop_;
  int epollfd_;
  EventList events_;
  ChannelMap channels_;
};

} // namespace zexuan::net
#endif  // NET_EPOLLER_H
